import 'package:flutter/material.dart';
import '../../domain/entities/payment_method.dart';
import '../../../../core/constants/my_colors.dart';

/// Payment Method Card Widget
/// Displays a payment method with interactive features
class PaymentMethodCard extends StatelessWidget {
  final PaymentMethod paymentMethod;
  final bool isSelected;
  final VoidCallback? onTap;
  final VoidCallback? onSetDefault;
  final VoidCallback? onDelete;

  const PaymentMethodCard({
    Key? key,
    required this.paymentMethod,
    this.isSelected = false,
    this.onTap,
    this.onSetDefault,
    this.onDelete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected
              ? MyColors.primary.withValues(alpha: 0.1)
              : Colors.grey.shade900,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? MyColors.primary : Colors.grey.shade700,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: MyColors.primary.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ]
              : null,
        ),
        child: Column(
          children: [
            Row(
              children: [
                // Payment Method Icon
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: _getPaymentMethodColor().withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getPaymentMethodIcon(),
                    color: _getPaymentMethodColor(),
                    size: 24,
                  ),
                ),

                const SizedBox(width: 16),

                // Payment Method Details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            paymentMethod.displayName,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          if (paymentMethod.isDefault) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: MyColors.primary,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                'Default',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getSubtitle(),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade400,
                        ),
                      ),
                    ],
                  ),
                ),

                // Selection Indicator
                if (isSelected)
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: MyColors.primary,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),

                // More Options
                PopupMenuButton<String>(
                  icon: Icon(
                    Icons.more_vert,
                    color: Colors.grey.shade400,
                  ),
                  color: Colors.grey.shade800,
                  onSelected: (value) {
                    switch (value) {
                      case 'set_default':
                        onSetDefault?.call();
                        break;
                      case 'delete':
                        onDelete?.call();
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    if (!paymentMethod.isDefault)
                      const PopupMenuItem(
                        value: 'set_default',
                        child: Row(
                          children: [
                            Icon(Icons.star, color: Colors.white, size: 18),
                            SizedBox(width: 8),
                            Text('Set as Default',
                                style: TextStyle(color: Colors.white)),
                          ],
                        ),
                      ),
                    if (paymentMethod.type != PaymentMethodType.cash)
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red, size: 18),
                            SizedBox(width: 8),
                            Text('Delete', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                  ],
                ),
              ],
            ),

            // Additional Info for Cards
            if (paymentMethod.isCard && paymentMethod.cardNumber != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade800,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.credit_card,
                      color: Colors.grey.shade400,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      paymentMethod.maskedCardNumber,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade300,
                        fontFamily: 'monospace',
                      ),
                    ),
                    const Spacer(),
                    if (paymentMethod.expiryDate != null)
                      Text(
                        paymentMethod.expiryDate!,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade400,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  IconData _getPaymentMethodIcon() {
    switch (paymentMethod.type.toLowerCase()) {
      case 'visa':
        return Icons.credit_card;
      case 'mastercard':
        return Icons.credit_card;
      case 'paypal':
        return Icons.account_balance;
      case 'apple_pay':
        return Icons.phone_iphone;
      case 'google_pay':
        return Icons.android;
      case 'cash':
        return Icons.money;
      case 'wallet':
        return Icons.account_balance_wallet;
      case 'credit_card':
      case 'debit_card':
      default:
        return Icons.payment;
    }
  }

  Color _getPaymentMethodColor() {
    switch (paymentMethod.type.toLowerCase()) {
      case 'visa':
        return const Color(0xFF1A1F71);
      case 'mastercard':
        return const Color(0xFFEB001B);
      case 'paypal':
        return const Color(0xFF003087);
      case 'apple_pay':
        return Colors.black;
      case 'google_pay':
        return const Color(0xFF4285F4);
      case 'cash':
        return Colors.green;
      case 'wallet':
        return MyColors.primary;
      case 'credit_card':
      case 'debit_card':
      default:
        return Colors.blue;
    }
  }

  String _getSubtitle() {
    switch (paymentMethod.type.toLowerCase()) {
      case 'visa':
        return 'Visa ${paymentMethod.cardNumber != null ? '•••• ${paymentMethod.cardNumber!.substring(paymentMethod.cardNumber!.length - 4)}' : ''}';
      case 'mastercard':
        return 'Mastercard ${paymentMethod.cardNumber != null ? '•••• ${paymentMethod.cardNumber!.substring(paymentMethod.cardNumber!.length - 4)}' : ''}';
      case 'paypal':
        return 'PayPal Account';
      case 'apple_pay':
        return 'Apple Pay';
      case 'google_pay':
        return 'Google Pay';
      case 'cash':
        return 'Pay with cash';
      case 'wallet':
        return 'Digital wallet';
      case 'credit_card':
        return 'Credit Card ${paymentMethod.cardNumber != null ? '•••• ${paymentMethod.cardNumber!.substring(paymentMethod.cardNumber!.length - 4)}' : ''}';
      case 'debit_card':
        return 'Debit Card ${paymentMethod.cardNumber != null ? '•••• ${paymentMethod.cardNumber!.substring(paymentMethod.cardNumber!.length - 4)}' : ''}';
      default:
        return PaymentMethodType.getDisplayName(paymentMethod.type);
    }
  }
}
