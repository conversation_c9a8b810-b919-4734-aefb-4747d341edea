# C/C++ build system timings
generate_cxx_metadata
  [gap of 47ms]
  create-invalidation-state 52ms
  [gap of 74ms]
  write-metadata-json-to-file 24ms
generate_cxx_metadata completed in 205ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 35ms]
  create-invalidation-state 54ms
  write-metadata-json-to-file 40ms
generate_cxx_metadata completed in 138ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 95ms]
  create-invalidation-state 183ms
  [gap of 15ms]
generate_cxx_metadata completed in 293ms

