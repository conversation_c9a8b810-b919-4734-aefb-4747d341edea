<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AndroidLayouts">
    <shared>
      <config />
    </shared>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="465981bf-eda1-4f29-a170-3dca02210aa7" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Dart File" />
      </list>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/lib/features/ride/views/package_delivery.dart" root0="SKIP_INSPECTION" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2tLVsGgIlf7CisIHC8jk8BCJRrb" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Flutter.main.dart.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.readMode.enableVisualFormatting&quot;: &quot;true&quot;,
    &quot;cf.first.check.clang-format&quot;: &quot;false&quot;,
    &quot;cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;com.codeium.enabled&quot;: &quot;true&quot;,
    &quot;dart.analysis.tool.window.visible&quot;: &quot;false&quot;,
    &quot;io.flutter.reload.alreadyRun&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/MA/Books/final release/project&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;Errors&quot;,
    &quot;show.migrate.to.gradle.popup&quot;: &quot;false&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\MA\me\projects\projects\project\assets\images" />
      <recent name="D:\MA\me\projects\projects\project\lib\features\auth\views\onboarding" />
      <recent name="D:\MA\me\projects\projects\project\lib\core\widgets\custom" />
      <recent name="D:\MA\me\projects\projects\project\lib\features\ride\views" />
      <recent name="D:\MA\me\projects\projects\project\lib\features\auth\views\sign_up" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\MA\me\projects\projects\project\lib\features\rider" />
      <recent name="D:\MA\Academey\final projects\project" />
      <recent name="D:\MA\Academey\final projects\project\supabase\functions" />
      <recent name="D:\MA\Academey\final projects\project\lib\features\ride" />
      <recent name="D:\MA\Academey\final projects\project\lib\core\themes" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="main.dart" type="FlutterRunConfigurationType" factoryName="Flutter">
      <option name="filePath" value="$PROJECT_DIR$/lib/main.dart" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SwiftWorkspaceSettings">
    <option name="detectedToolchain" value="true" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="465981bf-eda1-4f29-a170-3dca02210aa7" name="Changes" comment="" />
      <created>1740131579271</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1740131579271</updated>
    </task>
    <servers />
  </component>
</project>