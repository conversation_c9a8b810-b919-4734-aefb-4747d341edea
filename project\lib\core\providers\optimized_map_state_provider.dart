import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:project/features/map/helpers/map_drivers_helper.dart';

/// مزود حالة الخريطة المحسن للأداء
/// يدير حالة الخريطة والعلامات والمسارات مع تحسينات الأداء المتقدمة
class OptimizedMapStateProvider extends ChangeNotifier {
  // حالة الخريطة
  bool _isMapInitialized = false;
  bool get isMapInitialized => _isMapInitialized;

  // متحكم الخريطة
  final Completer<GoogleMapController> _mapController =
      Completer<GoogleMapController>();
  Completer<GoogleMapController> get mapController => _mapController;

  // الموقع الحالي مع تحسينات الأداء
  LatLng? _currentPosition;
  LatLng? _lastKnownPosition;
  LatLng? get currentPosition => _currentPosition;

  // الموقع الافتراضي
  final LatLng _defaultPosition = const LatLng(24.7136, 46.6753); // الرياض
  LatLng get defaultPosition => _defaultPosition;

  // العلامات والمسارات مع تحسينات الأداء
  final Map<String, Marker> _markersMap = {};
  final Map<String, Polyline> _polylinesMap = {};
  Set<Marker> get markers => _markersMap.values.toSet();
  Set<Polyline> get polylines => _polylinesMap.values.toSet();

  // نقاط المسار
  List<LatLng> _routePoints = [];
  List<LatLng> get routePoints => _routePoints;

  // حالة التحميل والأخطاء
  bool _isLoading = false;
  bool get isLoading => _isLoading;
  String? _error;
  String? get error => _error;

  // تحسينات الأداء - المؤقتات والتحديثات
  Timer? _locationUpdateTimer;
  Timer? _markersUpdateTimer;
  DateTime? _lastLocationUpdate;
  DateTime? _lastMarkersUpdate;

  // إعدادات الأداء
  static const Duration _locationUpdateInterval = Duration(seconds: 8);
  static const Duration _markersUpdateInterval = Duration(seconds: 5);
  static const double _significantLocationChange = 15.0; // meters
  static const int _maxMarkersOnMap = 50; // حد أقصى للعلامات

  // تجميع التحديثات لتحسين الأداء
  Timer? _batchUpdateTimer;
  bool _hasPendingUpdates = false;
  static const Duration _batchUpdateDelay = Duration(milliseconds: 300);

  @override
  void dispose() {
    _locationUpdateTimer?.cancel();
    _markersUpdateTimer?.cancel();
    _batchUpdateTimer?.cancel();
    super.dispose();
  }

  /// تهيئة الخريطة مع تحسينات الأداء
  void initializeMap(GoogleMapController controller) {
    if (!_mapController.isCompleted) {
      _mapController.complete(controller);
      _isMapInitialized = true;
      _startPeriodicUpdates();
      _notifyListenersOptimized();
    }
  }

  /// بدء التحديثات الدورية المحسنة
  void _startPeriodicUpdates() {
    // تحديث الموقع بشكل دوري مع تحسينات
    _locationUpdateTimer = Timer.periodic(_locationUpdateInterval, (_) {
      _updateLocationIfNeeded();
    });

    // تحديث العلامات بشكل دوري
    _markersUpdateTimer = Timer.periodic(_markersUpdateInterval, (_) {
      _updateMarkersIfNeeded();
    });
  }

  /// تحديث الموقع فقط عند الحاجة
  Future<void> _updateLocationIfNeeded() async {
    if (_isLoading) return;

    final now = DateTime.now();
    if (_lastLocationUpdate != null &&
        now.difference(_lastLocationUpdate!).inSeconds <
            _locationUpdateInterval.inSeconds) {
      return;
    }

    try {
      final position = await Geolocator.getCurrentPosition(
        locationSettings: LocationSettings(
          accuracy:
              LocationAccuracy.medium, // استخدام دقة متوسطة لتوفير البطارية
          timeLimit: const Duration(seconds: 5),
        ),
      );

      final newPosition = LatLng(position.latitude, position.longitude);

      // تحديث فقط إذا كان التغيير كبير
      if (_shouldUpdateLocation(newPosition)) {
        _currentPosition = newPosition;
        _lastLocationUpdate = now;
        _updateCurrentLocationMarker();
        _notifyListenersOptimized();
      }
    } catch (e) {
      // تسجيل الخطأ دون إيقاف التطبيق
      debugPrint('Location update error: $e');
    }
  }

  /// تحديث العلامات فقط عند الحاجة
  Future<void> _updateMarkersIfNeeded() async {
    if (_currentPosition == null || _isLoading) return;

    final now = DateTime.now();
    if (_lastMarkersUpdate != null &&
        now.difference(_lastMarkersUpdate!).inSeconds <
            _markersUpdateInterval.inSeconds) {
      return;
    }

    _lastMarkersUpdate = now;
    await _loadNearbyDriversOptimized();
  }

  /// تحديث السائقين القريبين مع تحسينات الأداء
  Future<void> _loadNearbyDriversOptimized() async {
    if (_currentPosition == null) return;

    try {
      final driverMarkers = await MapDriversHelper.loadNearbyDriversAsMarkers(
        location: _currentPosition!,
        radius: 3.0, // تقليل نطاق البحث لتحسين الأداء
        useCache: true,
        onError: (error) {
          _error = error;
        },
      );

      // تحديث العلامات مع الحد الأقصى
      _updateDriverMarkersOptimized(driverMarkers);
      _notifyListenersOptimized();
    } catch (e) {
      debugPrint('Drivers update error: $e');
    }
  }

  /// تحديث علامات السائقين مع تحسينات الأداء
  void _updateDriverMarkersOptimized(Set<Marker> newDriverMarkers) {
    // إزالة العلامات القديمة للسائقين
    _markersMap.removeWhere((key, value) => key.startsWith('driver_'));

    // إضافة العلامات الجديدة مع الحد الأقصى
    int addedCount = 0;
    for (final marker in newDriverMarkers) {
      if (addedCount >= _maxMarkersOnMap) break;
      _markersMap['driver_${marker.markerId.value}'] = marker;
      addedCount++;
    }
  }

  /// تحديث علامة الموقع الحالي
  void _updateCurrentLocationMarker() {
    if (_currentPosition != null) {
      _markersMap['current_location'] = Marker(
        markerId: const MarkerId('current_location'),
        position: _currentPosition!,
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
        infoWindow: const InfoWindow(title: 'موقعك الحالي'),
      );
    }
  }

  /// تحديد ما إذا كان يجب تحديث الموقع
  bool _shouldUpdateLocation(LatLng newPosition) {
    if (_lastKnownPosition == null) {
      _lastKnownPosition = newPosition;
      return true;
    }

    final distance = Geolocator.distanceBetween(
      _lastKnownPosition!.latitude,
      _lastKnownPosition!.longitude,
      newPosition.latitude,
      newPosition.longitude,
    );

    if (distance > _significantLocationChange) {
      _lastKnownPosition = newPosition;
      return true;
    }

    return false;
  }

  /// إشعار المستمعين مع تحسينات الأداء
  void _notifyListenersOptimized() {
    if (_hasPendingUpdates) return;

    _hasPendingUpdates = true;
    _batchUpdateTimer?.cancel();
    _batchUpdateTimer = Timer(_batchUpdateDelay, () {
      _hasPendingUpdates = false;
      notifyListeners();
    });
  }

  /// الحصول على الموقع الحالي مع تحسينات
  Future<void> getCurrentLocation() async {
    if (_isLoading) return;

    _setLoading(true);
    try {
      // التحقق من أذونات الموقع
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('تم رفض أذونات الموقع');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('تم رفض أذونات الموقع بشكل دائم');
      }

      // الحصول على الموقع الحالي مع إعدادات محسنة
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(seconds: 8),
        ),
      );

      _currentPosition = LatLng(position.latitude, position.longitude);
      _updateCurrentLocationMarker();

      // تحريك الكاميرا إلى الموقع الحالي
      if (_isMapInitialized) {
        final controller = await _mapController.future;
        controller
            .animateCamera(CameraUpdate.newLatLngZoom(_currentPosition!, 15));
      }

      _notifyListenersOptimized();
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  /// إضافة علامة مع تحسينات الأداء
  void addMarkerOptimized(String key, Marker marker) {
    _markersMap[key] = marker;
    _notifyListenersOptimized();
  }

  /// إزالة علامة مع تحسينات الأداء
  void removeMarkerOptimized(String key) {
    _markersMap.remove(key);
    _notifyListenersOptimized();
  }

  /// مسح جميع العلامات
  void clearMarkers() {
    _markersMap.clear();
    _notifyListenersOptimized();
  }

  /// إضافة مسار مع تحسينات الأداء
  void addPolylineOptimized(String key, Polyline polyline) {
    _polylinesMap[key] = polyline;
    _notifyListenersOptimized();
  }

  /// مسح جميع المسارات
  void clearPolylines() {
    _polylinesMap.clear();
    _notifyListenersOptimized();
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    _notifyListenersOptimized();
  }

  /// تحريك الخريطة إلى موقع محدد مع تحسينات
  Future<void> animateToLocation(LatLng location, {double zoom = 15.0}) async {
    if (_isMapInitialized) {
      try {
        final controller = await _mapController.future;
        controller.animateCamera(CameraUpdate.newLatLngZoom(location, zoom));
      } catch (e) {
        _error = e.toString();
        _notifyListenersOptimized();
      }
    }
  }

  /// إيقاف التحديثات الدورية
  void stopPeriodicUpdates() {
    _locationUpdateTimer?.cancel();
    _markersUpdateTimer?.cancel();
    _batchUpdateTimer?.cancel();
  }

  /// استئناف التحديثات الدورية
  void resumePeriodicUpdates() {
    if (_isMapInitialized) {
      _startPeriodicUpdates();
    }
  }
}
