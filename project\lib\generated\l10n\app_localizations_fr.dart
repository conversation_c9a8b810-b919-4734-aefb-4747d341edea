// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appTitle => 'Partage de Trajets Intelligent';

  @override
  String get welcome => 'Bienvenue';

  @override
  String get getStarted => 'Commencer';

  @override
  String get login => 'Se connecter';

  @override
  String get signUp => 'S\'inscrire';

  @override
  String get email => 'Email';

  @override
  String get password => 'Mot de passe';

  @override
  String get confirmPassword => 'Confirmer le mot de passe';

  @override
  String get firstName => 'Prénom';

  @override
  String get lastName => 'Nom de famille';

  @override
  String get phoneNumber => 'Numéro de téléphone';

  @override
  String get forgotPassword => 'Mot de passe oublié?';

  @override
  String get home => 'Accueil';

  @override
  String get profile => 'Profil';

  @override
  String get history => 'Historique';

  @override
  String get wallet => 'Portefeuille';

  @override
  String get offers => 'Offres';

  @override
  String get notifications => 'Notifications';

  @override
  String get whereWouldYouGo => 'Où voulez-vous aller?';

  @override
  String get searchForPlaces => 'Rechercher des lieux...';

  @override
  String get selectAddress => 'Sélectionner l\'adresse';

  @override
  String get from => 'De';

  @override
  String get to => 'À';

  @override
  String get previewRoute => 'Aperçu de l\'itinéraire';

  @override
  String get requestRide => 'Demander un trajet';

  @override
  String get recentPlaces => 'Lieux récents';

  @override
  String get work => 'Travail';

  @override
  String get chooseDriver => 'Choisir un chauffeur';

  @override
  String get availableDriversNearby => 'Chauffeurs disponibles à proximité';

  @override
  String get rating => 'Évaluation';

  @override
  String get distance => 'Distance';

  @override
  String get estimatedTime => 'Temps estimé';

  @override
  String get price => 'Prix';

  @override
  String get cancel => 'Annuler';

  @override
  String get confirm => 'Confirmer';

  @override
  String get accept => 'Accepter';

  @override
  String get reject => 'Rejeter';

  @override
  String get waiting => 'En attente';

  @override
  String get searchingForDriver => 'Recherche d\'un chauffeur...';

  @override
  String get pleaseWaitWhileWeSearch => 'Veuillez patienter pendant que nous trouvons le meilleur chauffeur pour vous';

  @override
  String get rideAccepted => 'Trajet accepté! Préparation de votre trajet...';

  @override
  String get rideRejected => 'Demande de trajet rejetée';

  @override
  String get rideStarted => 'Trajet commencé! Le chauffeur se dirige vers la destination...';

  @override
  String get driverUnavailable => 'Le chauffeur n\'est pas disponible actuellement';

  @override
  String get noDriversAvailable => 'Aucun chauffeur disponible pour le moment';

  @override
  String get driverArrivedAtPickup => 'Le chauffeur est arrivé au point de prise en charge';

  @override
  String get driverArrivedAtDestination => 'Le chauffeur est arrivé à destination';

  @override
  String get error => 'Erreur';

  @override
  String get ok => 'OK';

  @override
  String get retry => 'Réessayer';

  @override
  String get loading => 'Chargement...';

  @override
  String get noNotifications => 'Aucune notification disponible';

  @override
  String get refreshAndStartOver => 'Actualiser et recommencer';

  @override
  String get selectRole => 'Sélectionnez votre rôle';

  @override
  String get rider => 'Passager';

  @override
  String get driver => 'Chauffeur';

  @override
  String get car => 'Voiture';

  @override
  String get motorcycle => 'Motorcycle';

  @override
  String get delivery => 'Livraison';
}
