/// Payment Method Entity
/// Represents a payment method in the domain layer
class PaymentMethod {
  final String id;
  final String type;
  final String displayName;
  final String? cardNumber;
  final String? expiryDate;
  final String? cardHolderName;
  final String? bankName;
  final bool isDefault;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? metadata;

  const PaymentMethod({
    required this.id,
    required this.type,
    required this.displayName,
    this.cardNumber,
    this.expiryDate,
    this.cardHolderName,
    this.bankName,
    this.isDefault = false,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  /// Create a copy with updated fields
  PaymentMethod copyWith({
    String? id,
    String? type,
    String? displayName,
    String? cardNumber,
    String? expiryDate,
    String? cardHolderName,
    String? bankName,
    bool? isDefault,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return PaymentMethod(
      id: id ?? this.id,
      type: type ?? this.type,
      displayName: displayName ?? this.displayName,
      cardNumber: cardNumber ?? this.cardNumber,
      expiryDate: expiryDate ?? this.expiryDate,
      cardHolderName: cardHolderName ?? this.cardHolderName,
      bankName: bankName ?? this.bankName,
      isDefault: isDefault ?? this.isDefault,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Get masked card number for display
  String get maskedCardNumber {
    if (cardNumber == null || cardNumber!.length < 4) {
      return '**** **** **** ****';
    }
    final lastFour = cardNumber!.substring(cardNumber!.length - 4);
    return '**** **** **** $lastFour';
  }

  /// Check if payment method is a card
  bool get isCard => ['credit_card', 'debit_card', 'visa', 'mastercard'].contains(type.toLowerCase());

  /// Check if payment method is digital wallet
  bool get isDigitalWallet => ['paypal', 'apple_pay', 'google_pay', 'samsung_pay'].contains(type.toLowerCase());

  /// Check if payment method is cash
  bool get isCash => type.toLowerCase() == 'cash';

  /// Get payment method icon based on type
  String get iconPath {
    switch (type.toLowerCase()) {
      case 'visa':
        return 'assets/icons/payment/visa.png';
      case 'mastercard':
        return 'assets/icons/payment/mastercard.png';
      case 'paypal':
        return 'assets/icons/payment/paypal.png';
      case 'apple_pay':
        return 'assets/icons/payment/apple_pay.png';
      case 'google_pay':
        return 'assets/icons/payment/google_pay.png';
      case 'cash':
        return 'assets/icons/payment/cash.png';
      case 'credit_card':
      case 'debit_card':
      default:
        return 'assets/icons/payment/credit_card.png';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PaymentMethod && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PaymentMethod(id: $id, type: $type, displayName: $displayName, isDefault: $isDefault)';
  }
}

/// Payment Transaction Entity
class PaymentTransaction {
  final String id;
  final String rideId;
  final String paymentMethodId;
  final double amount;
  final String currency;
  final PaymentStatus status;
  final String? description;
  final DateTime createdAt;
  final DateTime? processedAt;
  final String? transactionReference;
  final String? failureReason;
  final Map<String, dynamic>? metadata;

  const PaymentTransaction({
    required this.id,
    required this.rideId,
    required this.paymentMethodId,
    required this.amount,
    required this.currency,
    required this.status,
    this.description,
    required this.createdAt,
    this.processedAt,
    this.transactionReference,
    this.failureReason,
    this.metadata,
  });

  PaymentTransaction copyWith({
    String? id,
    String? rideId,
    String? paymentMethodId,
    double? amount,
    String? currency,
    PaymentStatus? status,
    String? description,
    DateTime? createdAt,
    DateTime? processedAt,
    String? transactionReference,
    String? failureReason,
    Map<String, dynamic>? metadata,
  }) {
    return PaymentTransaction(
      id: id ?? this.id,
      rideId: rideId ?? this.rideId,
      paymentMethodId: paymentMethodId ?? this.paymentMethodId,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      status: status ?? this.status,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      processedAt: processedAt ?? this.processedAt,
      transactionReference: transactionReference ?? this.transactionReference,
      failureReason: failureReason ?? this.failureReason,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Check if transaction is successful
  bool get isSuccessful => status == PaymentStatus.completed;

  /// Check if transaction is pending
  bool get isPending => status == PaymentStatus.pending;

  /// Check if transaction failed
  bool get isFailed => status == PaymentStatus.failed;

  @override
  String toString() {
    return 'PaymentTransaction(id: $id, amount: $amount $currency, status: $status)';
  }
}

/// Payment Status Enum
enum PaymentStatus {
  pending,
  processing,
  completed,
  failed,
  cancelled,
  refunded,
}

/// Payment Method Types
class PaymentMethodType {
  static const String creditCard = 'credit_card';
  static const String debitCard = 'debit_card';
  static const String visa = 'visa';
  static const String mastercard = 'mastercard';
  static const String paypal = 'paypal';
  static const String applePay = 'apple_pay';
  static const String googlePay = 'google_pay';
  static const String samsungPay = 'samsung_pay';
  static const String cash = 'cash';
  static const String wallet = 'wallet';
  static const String bankTransfer = 'bank_transfer';

  static List<String> get allTypes => [
        creditCard,
        debitCard,
        visa,
        mastercard,
        paypal,
        applePay,
        googlePay,
        samsungPay,
        cash,
        wallet,
        bankTransfer,
      ];

  static String getDisplayName(String type) {
    switch (type) {
      case creditCard:
        return 'Credit Card';
      case debitCard:
        return 'Debit Card';
      case visa:
        return 'Visa';
      case mastercard:
        return 'Mastercard';
      case paypal:
        return 'PayPal';
      case applePay:
        return 'Apple Pay';
      case googlePay:
        return 'Google Pay';
      case samsungPay:
        return 'Samsung Pay';
      case cash:
        return 'Cash';
      case wallet:
        return 'Wallet';
      case bankTransfer:
        return 'Bank Transfer';
      default:
        return type;
    }
  }
}

/// Wallet Entity
class Wallet {
  final String id;
  final String userId;
  final double balance;
  final String currency;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const Wallet({
    required this.id,
    required this.userId,
    required this.balance,
    required this.currency,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
  });

  Wallet copyWith({
    String? id,
    String? userId,
    double? balance,
    String? currency,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Wallet(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      balance: balance ?? this.balance,
      currency: currency ?? this.currency,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Check if wallet has sufficient balance
  bool hasSufficientBalance(double amount) => balance >= amount;

  /// Get formatted balance
  String get formattedBalance => '$balance $currency';

  @override
  String toString() {
    return 'Wallet(id: $id, balance: $formattedBalance, isActive: $isActive)';
  }
}
