const io = require('socket.io-client');

// Test socket connection to the backend
const socket = io('http://localhost:3000', {
  transports: ['websocket']
});

let testDriverId = 'test_driver_123';

socket.on('connect', () => {
  console.log('✅ Socket connected successfully:', socket.id);

  // Test driver registration
  socket.emit('driver:available', testDriverId);
  console.log('📡 Sent driver:available event for driver:', testDriverId);

  // Test ride request after a short delay
  setTimeout(() => {
    const testRideRequest = {
      riderId: 'test_rider_456',
      pickupLocation: { lat: 30.0444, lng: 31.2357 },
      destination: { lat: 30.0644, lng: 31.2557 }
    };

    socket.emit('ride:request', testRideRequest);
    console.log('🚗 Sent ride request:', testRideRequest);
  }, 2000);
});

socket.on('connect_error', (error) => {
  console.error('❌ Socket connection error:', error);
});

socket.on('disconnect', () => {
  console.log('🔌 Socket disconnected');
});

socket.on('ride:new_request', (data) => {
  console.log('🔔 Received new ride request:', data);

  // Simulate driver accepting the ride after 3 seconds
  setTimeout(() => {
    console.log('✅ Driver accepting ride:', data.id);
    socket.emit('ride:accept', {
      rideId: data.id,
      driverId: testDriverId
    });
  }, 3000);
});

socket.on('ride:accepted', (data) => {
  console.log('✅ Ride accepted by backend:', data);
});

socket.on('ride:rejected', (data) => {
  console.log('❌ Ride rejected:', data);
});

socket.on('ride:error', (data) => {
  console.log('⚠️ Ride error:', data);
});

socket.on('ride:created', (data) => {
  console.log('📝 Ride created:', data);
});

socket.on('ride:started', (data) => {
  console.log('🚀 Ride started:', data);
});

socket.on('ride:completed', (data) => {
  console.log('🏁 Ride completed:', data);
});

// Keep the script running for 30 seconds
setTimeout(() => {
  console.log('🏁 Test completed, disconnecting...');
  socket.disconnect();
  process.exit(0);
}, 30000);
