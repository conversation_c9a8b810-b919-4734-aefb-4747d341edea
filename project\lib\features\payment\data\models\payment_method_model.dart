import '../../domain/entities/payment_method.dart';

/// Payment Method Data Model
/// Handles serialization/deserialization for API communication
class PaymentMethodModel extends PaymentMethod {
  const PaymentMethodModel({
    required super.id,
    required super.type,
    required super.displayName,
    super.cardNumber,
    super.expiryDate,
    super.cardHolderName,
    super.bankName,
    super.isDefault,
    super.isActive,
    required super.createdAt,
    super.updatedAt,
    super.metadata,
  });

  /// Create from JSON
  factory PaymentMethodModel.fromJson(Map<String, dynamic> json) {
    return PaymentMethodModel(
      id: json['id'] as String,
      type: json['type'] as String,
      displayName: json['display_name'] as String,
      cardNumber: json['card_number'] as String?,
      expiryDate: json['expiry_date'] as String?,
      cardHolderName: json['card_holder_name'] as String?,
      bankName: json['bank_name'] as String?,
      isDefault: json['is_default'] as bool? ?? false,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'display_name': displayName,
      'card_number': cardNumber,
      'expiry_date': expiryDate,
      'card_holder_name': cardHolderName,
      'bank_name': bankName,
      'is_default': isDefault,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Create from domain entity
  factory PaymentMethodModel.fromEntity(PaymentMethod entity) {
    return PaymentMethodModel(
      id: entity.id,
      type: entity.type,
      displayName: entity.displayName,
      cardNumber: entity.cardNumber,
      expiryDate: entity.expiryDate,
      cardHolderName: entity.cardHolderName,
      bankName: entity.bankName,
      isDefault: entity.isDefault,
      isActive: entity.isActive,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      metadata: entity.metadata,
    );
  }

  /// Convert to domain entity
  PaymentMethod toEntity() {
    return PaymentMethod(
      id: id,
      type: type,
      displayName: displayName,
      cardNumber: cardNumber,
      expiryDate: expiryDate,
      cardHolderName: cardHolderName,
      bankName: bankName,
      isDefault: isDefault,
      isActive: isActive,
      createdAt: createdAt,
      updatedAt: updatedAt,
      metadata: metadata,
    );
  }
}

/// Payment Transaction Data Model
class PaymentTransactionModel extends PaymentTransaction {
  const PaymentTransactionModel({
    required super.id,
    required super.rideId,
    required super.paymentMethodId,
    required super.amount,
    required super.currency,
    required super.status,
    super.description,
    required super.createdAt,
    super.processedAt,
    super.transactionReference,
    super.failureReason,
    super.metadata,
  });

  /// Create from JSON
  factory PaymentTransactionModel.fromJson(Map<String, dynamic> json) {
    return PaymentTransactionModel(
      id: json['id'] as String,
      rideId: json['ride_id'] as String,
      paymentMethodId: json['payment_method_id'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      status: PaymentStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => PaymentStatus.pending,
      ),
      description: json['description'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      processedAt: json['processed_at'] != null 
          ? DateTime.parse(json['processed_at'] as String) 
          : null,
      transactionReference: json['transaction_reference'] as String?,
      failureReason: json['failure_reason'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ride_id': rideId,
      'payment_method_id': paymentMethodId,
      'amount': amount,
      'currency': currency,
      'status': status.name,
      'description': description,
      'created_at': createdAt.toIso8601String(),
      'processed_at': processedAt?.toIso8601String(),
      'transaction_reference': transactionReference,
      'failure_reason': failureReason,
      'metadata': metadata,
    };
  }

  /// Create from domain entity
  factory PaymentTransactionModel.fromEntity(PaymentTransaction entity) {
    return PaymentTransactionModel(
      id: entity.id,
      rideId: entity.rideId,
      paymentMethodId: entity.paymentMethodId,
      amount: entity.amount,
      currency: entity.currency,
      status: entity.status,
      description: entity.description,
      createdAt: entity.createdAt,
      processedAt: entity.processedAt,
      transactionReference: entity.transactionReference,
      failureReason: entity.failureReason,
      metadata: entity.metadata,
    );
  }

  /// Convert to domain entity
  PaymentTransaction toEntity() {
    return PaymentTransaction(
      id: id,
      rideId: rideId,
      paymentMethodId: paymentMethodId,
      amount: amount,
      currency: currency,
      status: status,
      description: description,
      createdAt: createdAt,
      processedAt: processedAt,
      transactionReference: transactionReference,
      failureReason: failureReason,
      metadata: metadata,
    );
  }
}

/// Wallet Data Model
class WalletModel extends Wallet {
  const WalletModel({
    required super.id,
    required super.userId,
    required super.balance,
    required super.currency,
    super.isActive,
    required super.createdAt,
    super.updatedAt,
  });

  /// Create from JSON
  factory WalletModel.fromJson(Map<String, dynamic> json) {
    return WalletModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      balance: (json['balance'] as num).toDouble(),
      currency: json['currency'] as String,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'balance': balance,
      'currency': currency,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// Create from domain entity
  factory WalletModel.fromEntity(Wallet entity) {
    return WalletModel(
      id: entity.id,
      userId: entity.userId,
      balance: entity.balance,
      currency: entity.currency,
      isActive: entity.isActive,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }

  /// Convert to domain entity
  Wallet toEntity() {
    return Wallet(
      id: id,
      userId: userId,
      balance: balance,
      currency: currency,
      isActive: isActive,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}
