{"inputs": ["D:\\MA\\Books\\final release\\project\\.dart_tool\\flutter_build\\49ebc79a62d242a149926a712dbe3054\\app.dill", "C:\\scr\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\icon_tree_shaker.dart", "C:\\scr\\flutter\\bin\\internal\\engine.version", "C:\\scr\\flutter\\bin\\internal\\engine.version", "C:\\scr\\flutter\\bin\\internal\\engine.version", "C:\\scr\\flutter\\bin\\internal\\engine.version", "D:\\MA\\Books\\final release\\project\\pubspec.yaml", "D:\\MA\\Books\\final release\\project\\assets\\images\\Car.jpg", "D:\\MA\\Books\\final release\\project\\assets\\images\\Cash.png", "D:\\MA\\Books\\final release\\project\\assets\\images\\check_tick.png", "D:\\MA\\Books\\final release\\project\\assets\\images\\City driver-rafiki.png", "D:\\MA\\Books\\final release\\project\\assets\\images\\convertible car-bro.png", "D:\\MA\\Books\\final release\\project\\assets\\images\\Credit Card.png", "D:\\MA\\Books\\final release\\project\\assets\\images\\div-1.jpg", "D:\\MA\\Books\\final release\\project\\assets\\images\\div.jpg", "D:\\MA\\Books\\final release\\project\\assets\\images\\facebook.jpg", "D:\\MA\\Books\\final release\\project\\assets\\images\\google.jpg", "D:\\MA\\Books\\final release\\project\\assets\\images\\googlee.png", "D:\\MA\\Books\\final release\\project\\assets\\images\\illes1.png", "D:\\MA\\Books\\final release\\project\\assets\\images\\image 3.jpg", "D:\\MA\\Books\\final release\\project\\assets\\images\\image 4.jpg", "D:\\MA\\Books\\final release\\project\\assets\\images\\image 5.jpg", "D:\\MA\\Books\\final release\\project\\assets\\images\\image 6.jpg", "D:\\MA\\Books\\final release\\project\\assets\\images\\logo-removebg-preview.png", "D:\\MA\\Books\\final release\\project\\assets\\images\\map.png", "D:\\MA\\Books\\final release\\project\\assets\\images\\modern_car.png", "D:\\MA\\Books\\final release\\project\\assets\\images\\modern_van.png", "D:\\MA\\Books\\final release\\project\\assets\\images\\no_messages.png", "D:\\MA\\Books\\final release\\project\\assets\\images\\offer_1.png", "D:\\MA\\Books\\final release\\project\\assets\\images\\offer_2.png", "D:\\MA\\Books\\final release\\project\\assets\\images\\offer_3.png", "D:\\MA\\Books\\final release\\project\\assets\\images\\onboarding1.jpg", "D:\\MA\\Books\\final release\\project\\assets\\images\\onboarding2.jpg", "D:\\MA\\Books\\final release\\project\\assets\\images\\onboarding3.jpg", "D:\\MA\\Books\\final release\\project\\assets\\images\\onboarding4.jpg", "D:\\MA\\Books\\final release\\project\\assets\\images\\shared_car.png", "D:\\MA\\Books\\final release\\project\\assets\\images\\shield.png", "D:\\MA\\Books\\final release\\project\\assets\\images\\sign_1.png", "D:\\MA\\Books\\final release\\project\\assets\\images\\van.png", "D:\\MA\\Books\\final release\\project\\assets\\icon\\cancel.png", "D:\\MA\\Books\\final release\\project\\assets\\icon\\car.png", "D:\\MA\\Books\\final release\\project\\assets\\icon\\delivery.png", "D:\\MA\\Books\\final release\\project\\assets\\icon\\location.png", "D:\\MA\\Books\\final release\\project\\assets\\icon\\package.png", "D:\\MA\\Books\\final release\\project\\assets\\icon\\payment.png", "D:\\MA\\Books\\final release\\project\\assets\\icon\\person.png", "D:\\MA\\Books\\final release\\project\\assets\\icon\\person2.png", "D:\\MA\\Books\\final release\\project\\assets\\icon\\personal.png", "D:\\MA\\Books\\final release\\project\\assets\\icon\\promotion.png", "D:\\MA\\Books\\final release\\project\\assets\\icon\\save.png", "D:\\MA\\Books\\final release\\project\\assets\\icon\\saved_places.png", "D:\\MA\\Books\\final release\\project\\assets\\icon\\vouchers.png", "D:\\MA\\Books\\final release\\project\\assets\\icon\\what_to_send.png", "D:\\MA\\Books\\final release\\project\\assets\\animations\\voice_waves.json", "D:\\MA\\Books\\final release\\project\\.env", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_sound-9.28.0\\assets\\js\\async_processor.js", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_sound-9.28.0\\assets\\js\\tau_web.js", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_sound_web-9.28.0\\howler\\howler.js", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_sound_web-9.28.0\\src\\flutter_sound.js", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_sound_web-9.28.0\\src\\flutter_sound_player.js", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_sound_web-9.28.0\\src\\flutter_sound_recorder.js", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_sound_web-9.28.0\\src\\flutter_sound_stream_processor.js", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\font_awesome_flutter-10.8.0\\lib\\fonts\\fa-brands-400.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\font_awesome_flutter-10.8.0\\lib\\fonts\\fa-regular-400.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\font_awesome_flutter-10.8.0\\lib\\fonts\\fa-solid-900.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rflutter_alert-2.0.7\\assets\\images\\close.png", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rflutter_alert-2.0.7\\assets\\images\\2.0x\\close.png", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rflutter_alert-2.0.7\\assets\\images\\3.0x\\close.png", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rflutter_alert-2.0.7\\assets\\images\\icon_error.png", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rflutter_alert-2.0.7\\assets\\images\\2.0x\\icon_error.png", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rflutter_alert-2.0.7\\assets\\images\\3.0x\\icon_error.png", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rflutter_alert-2.0.7\\assets\\images\\icon_info.png", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rflutter_alert-2.0.7\\assets\\images\\2.0x\\icon_info.png", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rflutter_alert-2.0.7\\assets\\images\\3.0x\\icon_info.png", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rflutter_alert-2.0.7\\assets\\images\\icon_success.png", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rflutter_alert-2.0.7\\assets\\images\\2.0x\\icon_success.png", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rflutter_alert-2.0.7\\assets\\images\\3.0x\\icon_success.png", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rflutter_alert-2.0.7\\assets\\images\\icon_warning.png", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rflutter_alert-2.0.7\\assets\\images\\2.0x\\icon_warning.png", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rflutter_alert-2.0.7\\assets\\images\\3.0x\\icon_warning.png", "C:\\scr\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "C:\\scr\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "D:\\MA\\Books\\final release\\project\\.dart_tool\\flutter_build\\49ebc79a62d242a149926a712dbe3054\\native_assets.json", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\archive-3.6.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\args-2.7.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers-6.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_android-5.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_darwin-6.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_linux-4.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_platform_interface-7.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_web-5.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_windows-4.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\auto_size_text-3.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\avatar_glow-3.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\badges-3.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\boolean_selector-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cached_network_image-3.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cached_network_image_platform_interface-4.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cached_network_image_web-1.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cross_file-0.3.4+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csslib-1.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\curved_navigation_bar-1.0.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus-11.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus_platform_interface-7.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fake_async-1.3.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_linux-0.9.3+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_macos-0.9.4+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_windows-0.9.3+4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fixnum-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_dotenv-5.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_lints-2.0.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-17.2.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_platform_interface-7.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_pin_code_fields-2.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_plugin_android_lifecycle-2.0.27\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_secure_storage-9.2.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_secure_storage_linux-1.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_secure_storage_macos-3.1.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_secure_storage_platform_interface-1.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_secure_storage_web-1.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_secure_storage_windows-3.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_sound-9.28.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_sound_platform_interface-9.28.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_sound_web-9.28.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_tts-3.8.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\font_awesome_flutter-10.8.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geocoding-3.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geocoding_android-3.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geocoding_ios-3.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geocoding_platform_interface-3.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator-13.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_android-4.6.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_apple-2.3.9\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_platform_interface-4.2.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_web-4.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_windows-0.2.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps-8.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps_flutter-2.10.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps_flutter_android-2.14.13\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps_flutter_ios-2.13.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps_flutter_platform_interface-2.11.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps_flutter_web-0.5.10+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps_webservice-0.0.20-nullsafety.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\html-0.15.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-0.13.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker-1.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_android-0.8.12+22\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_for_web-3.0.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_ios-0.8.12+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_linux-0.2.1+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_macos-0.2.1+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_windows-0.2.1+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.19.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\js-0.6.7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker-10.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.9\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lints-2.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\logger-2.5.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\logging-1.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lottie-3.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\matcher-0.12.17\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.16.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-1.0.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\nested-1.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\octo_image-2.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider-2.1.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.15\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler-11.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_android-12.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_apple-9.4.7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_html-0.1.3+5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_windows-0.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pin_code_fields-8.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pinput-5.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\provider-6.1.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rflutter_alert-2.0.7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.27.7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sanitize_html-2.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.5.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_web-2.4.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\socket_io_client-2.0.3+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\socket_io_common-2.0.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sprintf-7.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_android-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_darwin-2.4.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_platform_interface-2.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_transform-2.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\test_api-0.7.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\timezone-0.9.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\universal_platform-1.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vibration-2.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vibration_platform_interface-0.0.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vm_service-14.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.12.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32_registry-2.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xdg_directories-1.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\LICENSE", "C:\\scr\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "C:\\scr\\flutter\\packages\\flutter\\LICENSE", "D:\\MA\\Books\\final release\\project\\DOES_NOT_EXIST_RERUN_FOR_WILDCARD499894949"], "outputs": ["D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/Car.jpg", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/Cash.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/check_tick.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/City%20driver-rafiki.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/convertible%20car-bro.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/Credit%20Card.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/div-1.jpg", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/div.jpg", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/facebook.jpg", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/google.jpg", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/googlee.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/illes1.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/image%203.jpg", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/image%204.jpg", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/image%205.jpg", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/image%206.jpg", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/logo-removebg-preview.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/map.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/modern_car.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/modern_van.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/no_messages.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/offer_1.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/offer_2.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/offer_3.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/onboarding1.jpg", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/onboarding2.jpg", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/onboarding3.jpg", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/onboarding4.jpg", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/shared_car.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/shield.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/sign_1.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/van.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icon/cancel.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icon/car.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icon/delivery.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icon/location.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icon/package.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icon/payment.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icon/person.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icon/person2.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icon/personal.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icon/promotion.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icon/save.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icon/saved_places.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icon/vouchers.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icon/what_to_send.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/animations/voice_waves.json", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\.env", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/flutter_sound/assets/js/async_processor.js", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/flutter_sound/assets/js/tau_web.js", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/flutter_sound_web/howler/howler.js", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/flutter_sound_web/src/flutter_sound.js", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/flutter_sound_web/src/flutter_sound_player.js", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/flutter_sound_web/src/flutter_sound_recorder.js", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/flutter_sound_web/src/flutter_sound_stream_processor.js", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/rflutter_alert/assets/images/close.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/rflutter_alert/assets/images/2.0x/close.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/rflutter_alert/assets/images/3.0x/close.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/rflutter_alert/assets/images/icon_error.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/rflutter_alert/assets/images/2.0x/icon_error.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/rflutter_alert/assets/images/3.0x/icon_error.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/rflutter_alert/assets/images/icon_info.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/rflutter_alert/assets/images/2.0x/icon_info.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/rflutter_alert/assets/images/3.0x/icon_info.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/rflutter_alert/assets/images/icon_success.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/rflutter_alert/assets/images/2.0x/icon_success.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/rflutter_alert/assets/images/3.0x/icon_success.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/rflutter_alert/assets/images/icon_warning.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/rflutter_alert/assets/images/2.0x/icon_warning.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/rflutter_alert/assets/images/3.0x/icon_warning.png", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "D:\\MA\\Books\\final release\\project\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]}