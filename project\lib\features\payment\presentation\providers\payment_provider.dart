import 'package:flutter/material.dart';
import 'package:project/features/payment/domain/repositories/payment_repository.dart';
import '../../domain/entities/payment_method.dart';
import '../../domain/usecases/payment_usecases.dart';
import '../../data/repositories/payment_repository_impl.dart';

/// Payment Provider
/// Manages payment state and operations using clean architecture
class PaymentProvider extends ChangeNotifier {
  final PaymentRepositoryImpl _repository = PaymentRepositoryImpl();
  
  // Use cases
  late final ProcessPaymentUseCase _processPaymentUseCase;
  late final AddPaymentMethodUseCase _addPaymentMethodUseCase;
  late final GetPaymentMethodsUseCase _getPaymentMethodsUseCase;
  late final SetDefaultPaymentMethodUseCase _setDefaultPaymentMethodUseCase;
  late final AddMoneyToWalletUseCase _addMoneyToWalletUseCase;
  late final SimulatePaymentUseCase _simulatePaymentUseCase;
  late final RefundPaymentUseCase _refundPaymentUseCase;

  // State
  List<PaymentMethod> _paymentMethods = [];
  PaymentMethod? _selectedPaymentMethod;
  Wallet? _wallet;
  List<PaymentTransaction> _transactions = [];
  List<WalletTransaction> _walletTransactions = [];
  
  bool _isLoading = false;
  String? _error;
  PaymentTransaction? _lastTransaction;
  PaymentSimulationResult? _lastSimulation;

  // Getters
  List<PaymentMethod> get paymentMethods => _paymentMethods;
  PaymentMethod? get selectedPaymentMethod => _selectedPaymentMethod;
  Wallet? get wallet => _wallet;
  List<PaymentTransaction> get transactions => _transactions;
  List<WalletTransaction> get walletTransactions => _walletTransactions;
  bool get isLoading => _isLoading;
  String? get error => _error;
  PaymentTransaction? get lastTransaction => _lastTransaction;
  PaymentSimulationResult? get lastSimulation => _lastSimulation;

  // Computed properties
  bool get hasPaymentMethods => _paymentMethods.isNotEmpty;
  PaymentMethod? get defaultPaymentMethod => 
      _paymentMethods.where((m) => m.isDefault).isNotEmpty 
          ? _paymentMethods.where((m) => m.isDefault).first 
          : null;
  double get walletBalance => _wallet?.balance ?? 0.0;
  bool get hasWallet => _wallet != null;

  PaymentProvider() {
    _initializeUseCases();
  }

  void _initializeUseCases() {
    _processPaymentUseCase = ProcessPaymentUseCase(_repository);
    _addPaymentMethodUseCase = AddPaymentMethodUseCase(_repository);
    _getPaymentMethodsUseCase = GetPaymentMethodsUseCase(_repository);
    _setDefaultPaymentMethodUseCase = SetDefaultPaymentMethodUseCase(_repository);
    _addMoneyToWalletUseCase = AddMoneyToWalletUseCase(_repository);
    _simulatePaymentUseCase = SimulatePaymentUseCase(_repository);
    _refundPaymentUseCase = RefundPaymentUseCase(_repository);
  }

  /// Load payment methods for a user
  Future<void> loadPaymentMethods(String userId) async {
    _setLoading(true);
    _clearError();

    try {
      _paymentMethods = await _getPaymentMethodsUseCase.execute(userId);
      
      // Set selected payment method to default if none selected
      if (_selectedPaymentMethod == null && defaultPaymentMethod != null) {
        _selectedPaymentMethod = defaultPaymentMethod;
      }
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to load payment methods: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Add a new payment method
  Future<bool> addPaymentMethod(PaymentMethod paymentMethod) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _addPaymentMethodUseCase.execute(paymentMethod);
      
      if (result.isSuccess) {
        _paymentMethods.add(result.paymentMethod!);
        notifyListeners();
        return true;
      } else {
        _setError(result.errorMessage!);
        return false;
      }
    } catch (e) {
      _setError('Failed to add payment method: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Set default payment method
  Future<bool> setDefaultPaymentMethod(String userId, String paymentMethodId) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _setDefaultPaymentMethodUseCase.execute(userId, paymentMethodId);
      
      if (success) {
        // Update local state
        for (int i = 0; i < _paymentMethods.length; i++) {
          _paymentMethods[i] = _paymentMethods[i].copyWith(
            isDefault: _paymentMethods[i].id == paymentMethodId,
          );
        }
        
        _selectedPaymentMethod = _paymentMethods.where((m) => m.id == paymentMethodId).first;
        notifyListeners();
        return true;
      } else {
        _setError('Failed to set default payment method');
        return false;
      }
    } catch (e) {
      _setError('Failed to set default payment method: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Select a payment method
  void selectPaymentMethod(PaymentMethod paymentMethod) {
    _selectedPaymentMethod = paymentMethod;
    notifyListeners();
  }

  /// Process payment
  Future<bool> processPayment({
    required String rideId,
    required double amount,
    required String currency,
    String? description,
  }) async {
    if (_selectedPaymentMethod == null) {
      _setError('No payment method selected');
      return false;
    }

    _setLoading(true);
    _clearError();

    try {
      final result = await _processPaymentUseCase.execute(
        rideId: rideId,
        paymentMethodId: _selectedPaymentMethod!.id,
        amount: amount,
        currency: currency,
        description: description,
      );

      if (result.isSuccess) {
        _lastTransaction = result.transaction;
        _transactions.insert(0, result.transaction!);
        
        // Update wallet balance if wallet payment
        if (_selectedPaymentMethod!.type == PaymentMethodType.wallet) {
          await _loadWallet(_selectedPaymentMethod!.metadata?['user_id'] ?? 'default_user');
        }
        
        notifyListeners();
        return true;
      } else {
        _setError(result.errorMessage!);
        return false;
      }
    } catch (e) {
      _setError('Payment processing failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Simulate payment (for testing)
  Future<bool> simulatePayment({
    required double amount,
    required String currency,
    bool shouldSucceed = true,
  }) async {
    if (_selectedPaymentMethod == null) {
      _setError('No payment method selected');
      return false;
    }

    _setLoading(true);
    _clearError();

    try {
      _lastSimulation = await _simulatePaymentUseCase.execute(
        paymentMethodId: _selectedPaymentMethod!.id,
        amount: amount,
        currency: currency,
        shouldSucceed: shouldSucceed,
      );

      notifyListeners();
      return _lastSimulation!.success;
    } catch (e) {
      _setError('Payment simulation failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Load wallet information
  Future<void> loadWallet(String userId) async {
    await _loadWallet(userId);
  }

  Future<void> _loadWallet(String userId) async {
    try {
      _wallet = await _repository.getWallet(userId);
      _walletTransactions = await _repository.getWalletTransactions(userId);
      notifyListeners();
    } catch (e) {
      debugPrint('Failed to load wallet: $e');
    }
  }

  /// Add money to wallet
  Future<bool> addMoneyToWallet({
    required String userId,
    required double amount,
    required String paymentMethodId,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _addMoneyToWalletUseCase.execute(
        userId: userId,
        amount: amount,
        paymentMethodId: paymentMethodId,
      );

      if (result.isSuccess) {
        _wallet = result.wallet;
        await _loadWallet(userId); // Reload to get updated transactions
        return true;
      } else {
        _setError(result.errorMessage!);
        return false;
      }
    } catch (e) {
      _setError('Failed to add money to wallet: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Refund payment
  Future<bool> refundPayment(String transactionId, {double? amount}) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _refundPaymentUseCase.execute(transactionId, amount: amount);

      if (result.isSuccess) {
        _transactions.insert(0, result.transaction!);
        notifyListeners();
        return true;
      } else {
        _setError(result.errorMessage!);
        return false;
      }
    } catch (e) {
      _setError('Refund failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Get payment fees
  Future<PaymentFees?> getPaymentFees({
    required double amount,
    required String currency,
  }) async {
    if (_selectedPaymentMethod == null) return null;

    try {
      return await _repository.calculatePaymentFees(
        amount: amount,
        paymentMethodType: _selectedPaymentMethod!.type,
        currency: currency,
      );
    } catch (e) {
      debugPrint('Failed to calculate fees: $e');
      return null;
    }
  }

  /// Clear error
  void clearError() {
    _clearError();
  }

  /// Reset state
  void reset() {
    _paymentMethods.clear();
    _selectedPaymentMethod = null;
    _wallet = null;
    _transactions.clear();
    _walletTransactions.clear();
    _lastTransaction = null;
    _lastSimulation = null;
    _clearError();
    notifyListeners();
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }
}
