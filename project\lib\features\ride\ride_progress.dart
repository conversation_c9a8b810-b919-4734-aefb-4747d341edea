// ignore_for_file: must_be_immutable
import 'dart:async';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:project/core/constants/my_colors.dart';
import 'package:project/core/services/sound_effect_service.dart';
import 'package:project/core/utils/debug_logger.dart';
import 'package:project/features/map/services/car_animation_service.dart';
import 'package:project/features/map/services/car_asset_service.dart';
import 'package:project/features/ride/views/ride_rating_screen.dart';

class RideProgressScreen extends StatefulWidget {
  final String? rideId;
  final LatLng initialDriverLocation;
  final LatLng destination;

  const RideProgressScreen({
    Key? key,
    this.rideId,
    required this.initialDriverLocation,
    required this.destination,
  }) : super(key: key);

  @override
  State<RideProgressScreen> createState() => _RideProgressScreenState();
}

class _RideProgressScreenState extends State<RideProgressScreen> {
  final Completer<GoogleMapController> _mapController = Completer();
  final Set<Marker> _markers = {};
  final Set<Polyline> _polylines = {};
  BitmapDescriptor? _carIcon;

  // Mock data for testing car movement
  List<LatLng> _mockRoute = [];
  int _mockRouteIndex = 0;
  Timer? _mockDriverTimer;

  @override
  void initState() {
    super.initState();
    _loadCarIcon();
    _initializeCarAnimation();
    _generateMockRoute();

    // Start mock driver movement after a short delay
    Future.delayed(const Duration(seconds: 1), () {
      _startMockDriverMovement();
    });
  }

  @override
  void dispose() {
    _mockDriverTimer?.cancel();
    CarAnimationService.instance.dispose();
    super.dispose();
  }

  Future<void> _loadCarIcon() async {
    try {
      // Use a simple car icon for now
      _createSimpleCarIcon();

      // Log for debugging
      logger.info('Car icon loaded successfully', tag: 'RideProgress');
    } catch (e) {
      logger.error('Error loading car icon: $e', tag: 'RideProgress');
      // Fallback to a simple car icon if loading fails
      _createSimpleCarIcon();
    }
  }

  void _createSimpleCarIcon() {
    setState(() {
      _carIcon =
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);
    });
    logger.debug('Simple car icon created', tag: 'RideProgress');
  }

  void _initializeCarAnimation() {
    logger.info('Initializing car animation service', tag: 'RideProgress');

    CarAnimationService.instance.initialize(
      controller: _mapController,
      onMarkersUpdate: (markers) {
        setState(() {
          _markers.clear();
          _markers.addAll(markers);

          // Add destination marker
          _markers.add(
            Marker(
              markerId: const MarkerId('destination'),
              position: widget.destination,
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueRed),
            ),
          );
        });

        logger.debug('Markers updated: ${_markers.length} markers on map',
            tag: 'RideProgress');
      },
      onRouteUpdate: (updatedRoute) {
        // Update the polylines when the route changes
        setState(() {
          _updateRoutePolyline(updatedRoute);
        });

        logger.debug('Route updated with ${updatedRoute.length} points',
            tag: 'RideProgress');
      },
    );

    // Set initial car position
    CarAnimationService.instance.updateCarLocation(
      widget.initialDriverLocation,
      carIcon: _carIcon,
    );

    // Set the initial route
    CarAnimationService.instance.setRoute(_mockRoute);

    logger.info(
        'Car animation initialized with start: ${widget.initialDriverLocation.latitude},${widget.initialDriverLocation.longitude}, '
        'destination: ${widget.destination.latitude},${widget.destination.longitude}',
        tag: 'RideProgress');
  }

  void _generateMockRoute() {
    // Generate a route from initial location to destination
    final double latDiff =
        widget.destination.latitude - widget.initialDriverLocation.latitude;
    final double lngDiff =
        widget.destination.longitude - widget.initialDriverLocation.longitude;

    // Use more steps for smoother animation
    const int steps = 40;
    for (int i = 0; i <= steps; i++) {
      final double fraction = i / steps;
      final double lat =
          widget.initialDriverLocation.latitude + latDiff * fraction;
      final double lng =
          widget.initialDriverLocation.longitude + lngDiff * fraction;

      // Add some randomness for a more realistic path (smaller jitter for smoother path)
      final double jitter =
          0.0002 * (i % 2 == 0 ? 1 : -1) * (steps - i) / steps;

      _mockRoute.add(LatLng(lat + jitter, lng + jitter));
    }

    // Log the route for debugging
    logger.info('Generated mock route with ${_mockRoute.length} points',
        tag: 'RideProgress');
    logger.debug(
        'Start: ${widget.initialDriverLocation.latitude},${widget.initialDriverLocation.longitude}',
        tag: 'RideProgress');
    logger.debug(
        'End: ${widget.destination.latitude},${widget.destination.longitude}',
        tag: 'RideProgress');

    // Initialize the polyline for the route
    _updateRoutePolyline();
  }

  /// Updates the polyline to show only the remaining route
  void _updateRoutePolyline([List<LatLng>? updatedRoute]) {
    // Get the remaining route points (from current position to destination)
    final List<LatLng> remainingRoute =
        updatedRoute ?? _mockRoute.sublist(_mockRouteIndex);

    // Get the traveled route points
    final List<LatLng> traveledRoute =
        updatedRoute == null && _mockRouteIndex > 0
            ? _mockRoute.sublist(0, _mockRouteIndex + 1)
            : CarAnimationService.instance.getVisitedPoints();

    // Update the polyline with only the remaining points
    setState(() {
      _polylines.clear();
      _polylines.add(
        Polyline(
          polylineId: const PolylineId('route'),
          points: remainingRoute,
          color: Colors.blue,
          width: 5,
        ),
      );

      // Add a polyline for the traveled route (optional - shows in different color)
      if (traveledRoute.isNotEmpty) {
        _polylines.add(
          Polyline(
            polylineId: const PolylineId('traveled_route'),
            points: traveledRoute,
            color:
                Colors.grey.withAlpha(180), // Dimmed color for traveled route
            width: 4,
          ),
        );
      }
    });

    logger.debug(
        'Updated route polyline, ${remainingRoute.length} points remaining, ${traveledRoute.length} points traveled',
        tag: 'RideProgress');
  }

  /// Updates the route when a new rider is added
  void updateRouteForNewRider(List<LatLng> newRoute) {
    // Update the route in the car animation service
    CarAnimationService.instance.updateRouteForNewRider(newRoute);

    // Update the mock route for our local tracking
    _mockRoute = List.from(newRoute);

    // Reset the mock route index to the current position
    if (_mockRouteIndex > 0) {
      _mockRouteIndex = 0; // Reset to start of new route
    }

    // Update the polyline
    _updateRoutePolyline(newRoute);

    logger.info('Route updated for new rider with ${newRoute.length} points',
        tag: 'RideProgress');
  }

  void _startMockDriverMovement() {
    // Use a shorter interval for smoother animation
    _mockDriverTimer =
        Timer.periodic(const Duration(milliseconds: 1500), (timer) {
      if (_mockRouteIndex < _mockRoute.length - 1) {
        _mockRouteIndex++;

        // Update car location with animation
        CarAnimationService.instance.updateCarLocation(
          _mockRoute[_mockRouteIndex],
          carIcon: _carIcon,
          // Use a shorter animation duration for smoother movement
          animationDuration: const Duration(milliseconds: 1400),
        );

        // Update progress and route polyline
        setState(() {
          _progress = _mockRouteIndex / (_mockRoute.length - 1);
        });

        // Update the route polyline to show only remaining points
        _updateRoutePolyline();

        // Log for debugging
        logger.debug(
            'Car moved to point $_mockRouteIndex of ${_mockRoute.length - 1}',
            tag: 'RideProgress');
      } else {
        timer.cancel();
        logger.info('Route completed, showing arrival dialog',
            tag: 'RideProgress');
        _showArrivalDialog();
      }
    });

    logger.info('Started mock driver movement', tag: 'RideProgress');
  }

  void _showArrivalDialog() {
    // Play ride completed sound
    SoundEffectService.instance.playRideCompleted();

    logger.info('Showing arrival dialog', tag: 'RideProgress');

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('You have arrived!'),
        content: const Text('Thank you for using our service.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showRatingScreen();
            },
            child: const Text('Rate your ride'),
          ),
        ],
      ),
    );
  }

  void _showRatingScreen() {
    // Get mock driver info for demo
    final driverInfo = {
      'id': 'driver_${DateTime.now().millisecondsSinceEpoch}',
      'name': 'John Driver',
      'photo': 'https://randomuser.me/api/portraits/men/32.jpg',
      'car': 'Toyota Camry',
    };

    final String rideId =
        widget.rideId ?? 'ride_${DateTime.now().millisecondsSinceEpoch}';

    logger.info('Navigating to rating screen for ride: $rideId',
        tag: 'RideProgress');

    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => RideRatingScreen(
          rideId: rideId,
          driverId: driverInfo['id'] as String,
          driverName: driverInfo['name'] as String,
          driverPhoto: driverInfo['photo'] as String,
          carModel: driverInfo['car'] as String,
          fare: 15.75, // Mock fare for demo
        ),
      ),
    );
  }

  // Progress of the ride (0.0 to 1.0)
  double _progress = 0.0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MyColors.cBackgroundColor,
      body: SafeArea(
        top: false,
        child: SizedBox(
          width: double.maxFinite,
          child: Column(
            children: [
              _buildMapView(context),
              _buildDriverInfoRow(context),
              const SizedBox(height: 24),
              _buildArrivalInfoRow(context),
              const SizedBox(height: 8),
              _buildProgressBar(context),
              const SizedBox(height: 24),
              _buildActionButtonsRow(context),
              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressBar(BuildContext context) {
    return Container(
      width: double.maxFinite,
      margin: const EdgeInsets.symmetric(horizontal: 6),
      child: Container(
        height: 8,
        width: 416,
        decoration: BoxDecoration(
          color: Colors.grey,
          borderRadius: BorderRadius.circular(4),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: LinearProgressIndicator(
            value: _progress,
            backgroundColor: Colors.grey,
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.green),
          ),
        ),
      ),
    );
  }

  Widget _buildMapView(BuildContext context) {
    return SizedBox(
      height: 500,
      width: double.maxFinite,
      child: GoogleMap(
        mapType: MapType.normal,
        initialCameraPosition: CameraPosition(
          target: widget.initialDriverLocation,
          zoom: 15,
        ),
        onMapCreated: (GoogleMapController controller) {
          _mapController.complete(controller);
        },
        markers: _markers,
        polylines: _polylines,
        zoomControlsEnabled: true,
        zoomGesturesEnabled: true,
        myLocationButtonEnabled: false,
        myLocationEnabled: false,
        buildingsEnabled: true,
      ),
    );
  }

  /// Section Widget
  Widget _buildDriverInfoRow(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      width: double.maxFinite,
      decoration: BoxDecoration(color: Colors.black),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset('assets/icon/person2.png', height: 50, width: 50),
          Expanded(
            child: Padding(
              padding: EdgeInsets.all(4),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("John D.", style: TextStyle(color: Colors.white)),
                  Text(
                    "Toyota Camry • ABC 123",
                    style: TextStyle(color: Colors.white),
                  ),
                ],
              ),
            ),
          ),
          Icon(Icons.ice_skating_outlined, color: Colors.white),
        ],
      ),
    );
  }

  /// Section Widget
  Widget _buildArrivalInfoRow(BuildContext context) {
    return Container(
      width: double.maxFinite,
      margin: EdgeInsets.symmetric(horizontal: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text("Estimated Arrival", style: TextStyle(color: Colors.white)),
          Text("5 mins away", style: TextStyle(color: Colors.white)),
        ],
      ),
    );
  }

  /// Section Widget
  Widget _buildActionButtonsRow(BuildContext context) {
    return Container(
      width: double.maxFinite,
      margin: EdgeInsets.symmetric(horizontal: 6),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              style: ButtonStyle(
                shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(9),
                  ),
                ),
              ),
              child: Container(
                margin: EdgeInsets.only(right: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.directions_car_filled, color: Colors.white),
                    SizedBox(width: 4),
                    Text(
                      "Message",
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
              onPressed: () {},
            ),
          ),
          SizedBox(width: 6),
          Expanded(
            child: OutlinedButton(
              style: ButtonStyle(
                shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(9),
                  ),
                ),
                side: WidgetStateProperty.all<BorderSide>(
                  const BorderSide(color: Colors.red, width: 1), // Red border
                ),
              ),
              child: Container(
                margin: EdgeInsets.only(right: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.cancel_outlined, color: Colors.red),
                    SizedBox(width: 4),
                    Text("Cancel", style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
              onPressed: () {},
            ),
          ),
        ],
      ),
    );
  }
}
