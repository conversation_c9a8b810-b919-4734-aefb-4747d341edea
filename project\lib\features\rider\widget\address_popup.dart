import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../../../core/constants/my_colors.dart';

class AddressPopup extends StatefulWidget {
  final LatLng? currentPosition;
  final Function(String) onSearch;
  final Function(LatLng, LatLng) onRouteConfirmed;
  final Function(LatLng)? onRideRequested;
  final String? initialToAddress;
  final LatLng? initialToPosition;

  const AddressPopup({
    super.key,
    required this.currentPosition,
    required this.onSearch,
    required this.onRouteConfirmed,
    this.onRideRequested,
    this.initialToAddress,
    this.initialToPosition,
  });

  @override
  _AddressPopupState createState() => _AddressPopupState();
}

class _AddressPopupState extends State<AddressPopup> {
  late TextEditingController fromController;
  late TextEditingController toController;
  late String initialFromHint;
  LatLng? startPosition;
  LatLng? endPosition;
  bool _isLoading = false;
  List<String> _recentAddresses = [];
  List<String> _searchSuggestions = [];

  @override
  void initState() {
    super.initState();
    fromController = TextEditingController();
    toController = TextEditingController(text: widget.initialToAddress ?? '');
    initialFromHint = widget.currentPosition == null
        ? 'Current location not available'
        : _formatCurrentLocation();
    startPosition = widget.currentPosition;
    endPosition = widget.initialToPosition;
    _loadRecentAddresses();
  }

  String _formatCurrentLocation() {
    if (widget.currentPosition == null) return 'Current location not available';
    return 'Current Location (${widget.currentPosition!.latitude.toStringAsFixed(4)}, ${widget.currentPosition!.longitude.toStringAsFixed(4)})';
  }

  Future<void> _loadRecentAddresses() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      setState(() {
        _recentAddresses = prefs.getStringList('recent_addresses') ??
            [
              'Home - 123 Main Street',
              'Work - 456 Business District',
              'Mall - Shopping Center',
              'Airport - International Terminal',
            ];
      });
    } catch (e) {
      debugPrint('Error loading recent addresses: $e');
    }
  }

  Future<void> _saveRecentAddress(String address) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recent = prefs.getStringList('recent_addresses') ?? [];
      if (!recent.contains(address)) {
        recent.insert(0, address);
        if (recent.length > 10) recent.removeLast();
        await prefs.setStringList('recent_addresses', recent);
      }
    } catch (e) {
      debugPrint('Error saving recent address: $e');
    }
  }

  @override
  void dispose() {
    fromController.dispose();
    toController.dispose();
    super.dispose();
  }

  Future<void> _processAddresses() async {
    if (startPosition == null) {
      List<Location> fromLocations = await locationFromAddress(
        fromController.text,
      );
      if (fromLocations.isEmpty) {
        throw Exception('Invalid start address');
      }
      startPosition = LatLng(
        fromLocations.first.latitude,
        fromLocations.first.longitude,
      );
    }

    if (endPosition == null) {
      List<Location> toLocations = await locationFromAddress(
        toController.text,
      );
      if (toLocations.isEmpty) {
        throw Exception('Invalid end address');
      }
      endPosition = LatLng(
        toLocations.first.latitude,
        toLocations.first.longitude,
      );
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
          content: Text('${AppLocalizations.of(context)!.error}: $message')),
    );
  }

  IconData _getLocationIcon(String title) {
    switch (title.toLowerCase()) {
      case 'home':
        return Icons.home;
      case 'work':
        return Icons.work;
      case 'mall':
      case 'shopping':
        return Icons.shopping_bag;
      case 'airport':
        return Icons.flight;
      case 'hospital':
        return Icons.local_hospital;
      case 'school':
      case 'university':
        return Icons.school;
      case 'restaurant':
        return Icons.restaurant;
      case 'gas':
      case 'fuel':
        return Icons.local_gas_station;
      default:
        return Icons.place;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Container(
              width: 50,
              height: 5,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context)!.selectAddress,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: fromController,
            decoration: InputDecoration(
              hintText: initialFromHint,
              prefixIcon: const Icon(Icons.location_on_outlined),
              labelText: AppLocalizations.of(context)!.from,
              border: const OutlineInputBorder(),
            ),
            onChanged: (value) => setState(() {}),
            onSubmitted: (value) {
              if (value.isEmpty) {
                setState(() {
                  fromController.text = initialFromHint;
                });
              }
            },
          ),
          const SizedBox(height: 10),
          TextField(
            controller: toController,
            decoration: InputDecoration(
              prefixIcon: const Icon(Icons.location_on_outlined),
              labelText: AppLocalizations.of(context)!.to,
              border: const OutlineInputBorder(),
            ),
            onChanged: (value) => setState(() {}),
            onSubmitted: (value) {
              if (toController.text.isNotEmpty) {
                widget.onSearch(toController.text);
              }
            },
          ),
          const SizedBox(height: 16),
          if (fromController.text.isNotEmpty && toController.text.isNotEmpty)
            Column(
              children: [
                // Preview Route Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () async {
                      try {
                        await _processAddresses();
                        if (startPosition != null && endPosition != null) {
                          widget.onRouteConfirmed(startPosition!, endPosition!);
                        }
                      } catch (e) {
                        _showError(e.toString());
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      AppLocalizations.of(context)!.previewRoute,
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                // Request Ride Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () async {
                      try {
                        await _processAddresses();
                        if (endPosition != null &&
                            widget.onRideRequested != null) {
                          widget.onRideRequested!(endPosition!);
                          Navigator.of(context).pop(); // Close the popup
                        } else {
                          _showError('Please enter a valid destination');
                        }
                      } catch (e) {
                        _showError(e.toString());
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      AppLocalizations.of(context)!.requestRide,
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context)!.recentPlaces,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 200,
            child: ListView.builder(
              itemCount: _recentAddresses.length,
              itemBuilder: (context, index) {
                final address = _recentAddresses[index];
                final parts = address.split(' - ');
                final title = parts.isNotEmpty ? parts[0] : address;
                final subtitle = parts.length > 1 ? parts[1] : '';

                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 2),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: MyColors.primary.withValues(alpha: 0.1),
                      child: Icon(
                        _getLocationIcon(title),
                        color: MyColors.primary,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      title,
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    subtitle: subtitle.isNotEmpty ? Text(subtitle) : null,
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      setState(() {
                        toController.text = address;
                      });
                      widget.onSearch(address);
                    },
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: 16),

          // Quick action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    setState(() {
                      toController.text = 'Home';
                    });
                    widget.onSearch('Home');
                  },
                  icon: const Icon(Icons.home),
                  label: const Text('Home'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: MyColors.primary,
                    side: BorderSide(color: MyColors.primary),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    setState(() {
                      toController.text = 'Work';
                    });
                    widget.onSearch('Work');
                  },
                  icon: const Icon(Icons.work),
                  label: const Text('Work'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: MyColors.primary,
                    side: BorderSide(color: MyColors.primary),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
