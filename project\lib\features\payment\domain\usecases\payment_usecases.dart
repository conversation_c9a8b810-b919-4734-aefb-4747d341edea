import '../entities/payment_method.dart';
import '../repositories/payment_repository.dart';

/// Process Payment Use Case
class ProcessPaymentUseCase {
  final PaymentRepository repository;

  ProcessPaymentUseCase(this.repository);

  Future<PaymentResult> execute({
    required String rideId,
    required String paymentMethodId,
    required double amount,
    required String currency,
    String? description,
  }) async {
    try {
      // Validate payment method
      final paymentMethod = await repository.getPaymentMethod(paymentMethodId);
      if (paymentMethod == null) {
        return PaymentResult.failure('Payment method not found');
      }

      if (!paymentMethod.isActive) {
        return PaymentResult.failure('Payment method is not active');
      }

      // Calculate fees
      final fees = await repository.calculatePaymentFees(
        amount: amount,
        paymentMethodType: paymentMethod.type,
        currency: currency,
      );

      final totalAmount = amount + fees.totalFees;

      // For wallet payments, check balance
      if (paymentMethod.type == PaymentMethodType.wallet) {
        final wallet = await repository.getWallet(paymentMethod.id);
        if (wallet == null || !wallet.hasSufficientBalance(totalAmount)) {
          return PaymentResult.failure('Insufficient wallet balance');
        }
      }

      // Process payment
      final transaction = await repository.processPayment(
        rideId: rideId,
        paymentMethodId: paymentMethodId,
        amount: totalAmount,
        currency: currency,
        description: description ?? 'Ride payment',
        metadata: {
          'original_amount': amount,
          'fees': fees.totalFees,
          'fee_breakdown': fees.breakdown,
        },
      );

      return PaymentResult.success(transaction);
    } catch (e) {
      return PaymentResult.failure('Payment processing failed: $e');
    }
  }
}

/// Add Payment Method Use Case
class AddPaymentMethodUseCase {
  final PaymentRepository repository;

  AddPaymentMethodUseCase(this.repository);

  Future<PaymentMethodResult> execute(PaymentMethod paymentMethod) async {
    try {
      // Validate payment method
      final isValid = await repository.validatePaymentMethod(paymentMethod);
      if (!isValid) {
        return PaymentMethodResult.failure('Invalid payment method details');
      }

      // Check if payment method type is supported
      final isSupported = await repository.isPaymentMethodSupported(paymentMethod.type);
      if (!isSupported) {
        return PaymentMethodResult.failure('Payment method type not supported');
      }

      // Add payment method
      final addedPaymentMethod = await repository.addPaymentMethod(paymentMethod);
      return PaymentMethodResult.success(addedPaymentMethod);
    } catch (e) {
      return PaymentMethodResult.failure('Failed to add payment method: $e');
    }
  }
}

/// Get Payment Methods Use Case
class GetPaymentMethodsUseCase {
  final PaymentRepository repository;

  GetPaymentMethodsUseCase(this.repository);

  Future<List<PaymentMethod>> execute(String userId) async {
    return await repository.getPaymentMethods(userId);
  }
}

/// Set Default Payment Method Use Case
class SetDefaultPaymentMethodUseCase {
  final PaymentRepository repository;

  SetDefaultPaymentMethodUseCase(this.repository);

  Future<bool> execute(String userId, String paymentMethodId) async {
    try {
      return await repository.setDefaultPaymentMethod(userId, paymentMethodId);
    } catch (e) {
      return false;
    }
  }
}

/// Add Money to Wallet Use Case
class AddMoneyToWalletUseCase {
  final PaymentRepository repository;

  AddMoneyToWalletUseCase(this.repository);

  Future<WalletResult> execute({
    required String userId,
    required double amount,
    required String paymentMethodId,
  }) async {
    try {
      if (amount <= 0) {
        return WalletResult.failure('Amount must be greater than zero');
      }

      // Validate payment method
      final paymentMethod = await repository.getPaymentMethod(paymentMethodId);
      if (paymentMethod == null || !paymentMethod.isActive) {
        return WalletResult.failure('Invalid payment method');
      }

      // Add money to wallet
      final wallet = await repository.addMoneyToWallet(
        userId: userId,
        amount: amount,
        paymentMethodId: paymentMethodId,
      );

      return WalletResult.success(wallet);
    } catch (e) {
      return WalletResult.failure('Failed to add money to wallet: $e');
    }
  }
}

/// Simulate Payment Use Case (for testing)
class SimulatePaymentUseCase {
  final PaymentRepository repository;

  SimulatePaymentUseCase(this.repository);

  Future<PaymentSimulationResult> execute({
    required String paymentMethodId,
    required double amount,
    required String currency,
    bool shouldSucceed = true,
  }) async {
    return await repository.simulatePayment(
      paymentMethodId: paymentMethodId,
      amount: amount,
      currency: currency,
      shouldSucceed: shouldSucceed,
    );
  }
}

/// Refund Payment Use Case
class RefundPaymentUseCase {
  final PaymentRepository repository;

  RefundPaymentUseCase(this.repository);

  Future<PaymentResult> execute(String transactionId, {double? amount}) async {
    try {
      // Get original transaction
      final originalTransaction = await repository.getPaymentTransaction(transactionId);
      if (originalTransaction == null) {
        return PaymentResult.failure('Transaction not found');
      }

      if (!originalTransaction.isSuccessful) {
        return PaymentResult.failure('Cannot refund unsuccessful transaction');
      }

      // Process refund
      final refundTransaction = await repository.refundPayment(transactionId, amount: amount);
      return PaymentResult.success(refundTransaction);
    } catch (e) {
      return PaymentResult.failure('Refund failed: $e');
    }
  }
}

/// Result Classes
class PaymentResult {
  final bool isSuccess;
  final PaymentTransaction? transaction;
  final String? errorMessage;

  PaymentResult._(this.isSuccess, this.transaction, this.errorMessage);

  factory PaymentResult.success(PaymentTransaction transaction) {
    return PaymentResult._(true, transaction, null);
  }

  factory PaymentResult.failure(String errorMessage) {
    return PaymentResult._(false, null, errorMessage);
  }

  @override
  String toString() {
    return 'PaymentResult(isSuccess: $isSuccess, errorMessage: $errorMessage)';
  }
}

class PaymentMethodResult {
  final bool isSuccess;
  final PaymentMethod? paymentMethod;
  final String? errorMessage;

  PaymentMethodResult._(this.isSuccess, this.paymentMethod, this.errorMessage);

  factory PaymentMethodResult.success(PaymentMethod paymentMethod) {
    return PaymentMethodResult._(true, paymentMethod, null);
  }

  factory PaymentMethodResult.failure(String errorMessage) {
    return PaymentMethodResult._(false, null, errorMessage);
  }

  @override
  String toString() {
    return 'PaymentMethodResult(isSuccess: $isSuccess, errorMessage: $errorMessage)';
  }
}

class WalletResult {
  final bool isSuccess;
  final Wallet? wallet;
  final String? errorMessage;

  WalletResult._(this.isSuccess, this.wallet, this.errorMessage);

  factory WalletResult.success(Wallet wallet) {
    return WalletResult._(true, wallet, null);
  }

  factory WalletResult.failure(String errorMessage) {
    return WalletResult._(false, null, errorMessage);
  }

  @override
  String toString() {
    return 'WalletResult(isSuccess: $isSuccess, errorMessage: $errorMessage)';
  }
}
