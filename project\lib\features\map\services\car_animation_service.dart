import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:project/core/utils/debug_logger.dart';

/// تحسينات الأداء للرسوم المتحركة
class AnimationPerformanceConfig {
  static const Duration defaultAnimationDuration = Duration(milliseconds: 1500);
  static const Duration fastAnimationDuration = Duration(milliseconds: 800);
  static const Duration slowAnimationDuration = Duration(milliseconds: 2500);
  static const int maxAnimationSteps = 30;
  static const double minDistanceForAnimation = 5.0; // meters
}

/// A service to handle realistic car movement animations on Google Maps
class CarAnimationService {
  static final CarAnimationService _instance = CarAnimationService._internal();
  static CarAnimationService get instance => _instance;

  CarAnimationService._internal();

  /// The previous location of the car
  LatLng? _previousLocation;

  /// The current location of the car
  LatLng? _currentLocation;

  /// The bearing (direction) of the car in degrees
  double _bearing = 0.0;

  /// Timer for animation updates
  Timer? _animationTimer;

  /// Controller for the Google Map
  Completer<GoogleMapController>? _mapController;

  /// Set of markers on the map
  final Set<Marker> _markers = {};

  /// Set of visited points (to be removed from the route)
  final List<LatLng> _visitedPoints = [];

  /// Current route points
  List<LatLng> _currentRoute = [];

  /// Callback for when markers are updated
  Function(Set<Marker>)? _onMarkersUpdate;

  /// Callback for when route is updated
  Function(List<LatLng>)? _onRouteUpdate;

  /// Initialize the service with a map controller and callbacks
  void initialize({
    required Completer<GoogleMapController> controller,
    required Function(Set<Marker>) onMarkersUpdate,
    Function(List<LatLng>)? onRouteUpdate,
  }) {
    _mapController = controller;
    _onMarkersUpdate = onMarkersUpdate;
    _onRouteUpdate = onRouteUpdate;
  }

  /// Set the current route
  void setRoute(List<LatLng> route) {
    _currentRoute = List.from(route);
    _visitedPoints.clear();

    // Notify listeners if callback is provided
    if (_onRouteUpdate != null) {
      _onRouteUpdate!(_currentRoute);
    }

    logger.info('Route set with ${_currentRoute.length} points',
        tag: 'CarAnimation');
  }

  /// Update the car's location with optimized smooth animation
  Future<void> updateCarLocation(
    LatLng newLocation, {
    String markerId = 'car_marker',
    BitmapDescriptor? carIcon,
    Duration? animationDuration,
    bool updateVisitedPoints = true,
  }) async {
    if (_previousLocation == null) {
      // First update, just set the location without animation
      _previousLocation = newLocation;
      _currentLocation = newLocation;
      _updateMarker(markerId, newLocation, 0.0, carIcon);
      return;
    }

    // Calculate distance to determine if animation is needed
    final distance = _calculateDistance(_currentLocation!, newLocation);

    // Skip animation for very small movements to improve performance
    if (distance < AnimationPerformanceConfig.minDistanceForAnimation) {
      _currentLocation = newLocation;
      _updateMarker(markerId, newLocation, _bearing, carIcon);
      return;
    }

    // Calculate bearing between previous and new location
    _previousLocation = _currentLocation;
    _currentLocation = newLocation;
    _bearing = _calculateBearing(_previousLocation!, newLocation);

    // Determine optimal animation duration based on distance
    final optimalDuration =
        animationDuration ?? _getOptimalAnimationDuration(distance);

    // Start optimized animation
    _startAnimation(markerId, _previousLocation!, newLocation, _bearing,
        carIcon, optimalDuration);

    // Track visited points if enabled
    if (updateVisitedPoints) {
      _updateVisitedPoints(newLocation);
    }

    // Log movement details with our logger (reduced frequency)
    if (distance > 20.0) {
      // Only log significant movements
      logger.debug(
          'Car moving ${distance.toStringAsFixed(1)}m with bearing ${_bearing.toStringAsFixed(1)}°',
          tag: 'CarAnimation');
    }
  }

  /// Calculate distance between two points in meters
  double _calculateDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371000; // Earth radius in meters
    final double lat1Rad = point1.latitude * math.pi / 180;
    final double lat2Rad = point2.latitude * math.pi / 180;
    final double deltaLatRad =
        (point2.latitude - point1.latitude) * math.pi / 180;
    final double deltaLngRad =
        (point2.longitude - point1.longitude) * math.pi / 180;

    final double a = math.sin(deltaLatRad / 2) * math.sin(deltaLatRad / 2) +
        math.cos(lat1Rad) *
            math.cos(lat2Rad) *
            math.sin(deltaLngRad / 2) *
            math.sin(deltaLngRad / 2);
    final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));

    return earthRadius * c;
  }

  /// Get optimal animation duration based on distance
  Duration _getOptimalAnimationDuration(double distance) {
    if (distance < 50) {
      return AnimationPerformanceConfig.fastAnimationDuration;
    } else if (distance < 200) {
      return AnimationPerformanceConfig.defaultAnimationDuration;
    } else {
      return AnimationPerformanceConfig.slowAnimationDuration;
    }
  }

  /// Update the list of visited points and remove them from the current route
  void _updateVisitedPoints(LatLng currentLocation) {
    if (_currentRoute.isEmpty) return;

    // Add the current point to visited points
    _visitedPoints.add(currentLocation);

    // Find the closest point in the route to the current location
    int closestPointIndex = _findClosestPointIndex(currentLocation);

    // If we found a valid point, remove all points up to and including it
    if (closestPointIndex >= 0 && closestPointIndex < _currentRoute.length) {
      // Remove all points up to the closest point
      if (closestPointIndex > 0) {
        _currentRoute.removeRange(0, closestPointIndex);

        // Notify listeners about the updated route
        if (_onRouteUpdate != null) {
          _onRouteUpdate!(_currentRoute);
        }

        logger.debug(
            'Removed ${closestPointIndex} visited points from route, ${_currentRoute.length} points remaining',
            tag: 'CarAnimation');
      }
    }
  }

  /// Find the index of the closest point in the route to the given location
  int _findClosestPointIndex(LatLng location) {
    if (_currentRoute.isEmpty) return -1;

    double minDistance = double.infinity;
    int closestIndex = -1;

    // Find the closest point within a reasonable threshold
    const double threshold = 0.0005; // Approximately 50 meters

    for (int i = 0; i < _currentRoute.length; i++) {
      double distance = _calculateDistance(location, _currentRoute[i]);

      if (distance < minDistance && distance < threshold) {
        minDistance = distance;
        closestIndex = i;
      }
    }

    return closestIndex;
  }

  /// Calculate the bearing (direction) between two points
  double _calculateBearing(LatLng start, LatLng end) {
    double lat1 = start.latitude * math.pi / 180;
    double lng1 = start.longitude * math.pi / 180;
    double lat2 = end.latitude * math.pi / 180;
    double lng2 = end.longitude * math.pi / 180;

    double dLon = lng2 - lng1;
    double y = math.sin(dLon) * math.cos(lat2);
    double x = math.cos(lat1) * math.sin(lat2) -
        math.sin(lat1) * math.cos(lat2) * math.cos(dLon);
    double bearing = math.atan2(y, x);

    // Convert to degrees
    bearing = bearing * 180 / math.pi;
    bearing = (bearing + 360) % 360;

    return bearing;
  }

  /// Start the optimized animation between two points
  void _startAnimation(
    String markerId,
    LatLng start,
    LatLng end,
    double bearing,
    BitmapDescriptor? carIcon,
    Duration duration,
  ) {
    // Cancel any existing animation
    _animationTimer?.cancel();

    // Use fewer steps for better performance, but ensure smooth animation
    final int steps = math.min(AnimationPerformanceConfig.maxAnimationSteps,
        (duration.inMilliseconds / 50).round()); // 50ms per step minimum
    final int intervalMs = duration.inMilliseconds ~/ steps;
    int currentStep = 0;

    // Create a timer that fires at regular intervals
    _animationTimer =
        Timer.periodic(Duration(milliseconds: intervalMs), (timer) {
      currentStep++;

      if (currentStep <= steps) {
        // Calculate the interpolated position
        double fraction = currentStep / steps;

        // Apply easing for more realistic movement (ease in/out)
        fraction = _easeInOut(fraction);

        double lat =
            start.latitude + (end.latitude - start.latitude) * fraction;
        double lng =
            start.longitude + (end.longitude - start.longitude) * fraction;

        LatLng interpolatedPosition = LatLng(lat, lng);

        // Update the marker position
        _updateMarker(markerId, interpolatedPosition, bearing, carIcon);

        // Move the camera to follow the car if needed
        _moveCamera(interpolatedPosition);
      } else {
        // Animation complete
        timer.cancel();
      }
    });
  }

  /// Apply ease-in/ease-out effect for smoother acceleration/deceleration
  double _easeInOut(double t) {
    // Cubic ease in/out function
    return t < 0.5 ? 4 * t * t * t : 1 - math.pow(-2 * t + 2, 3) / 2;
  }

  /// Update the marker on the map
  void _updateMarker(
    String markerId,
    LatLng position,
    double rotation,
    BitmapDescriptor? icon,
  ) {
    final Marker marker = Marker(
      markerId: MarkerId(markerId),
      position: position,
      rotation: rotation,
      icon: icon ??
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure),
      anchor: const Offset(0.5, 0.5), // Center the icon
      flat: true, // Make the marker flat to enable rotation
    );

    _markers.removeWhere((m) => m.markerId.value == markerId);
    _markers.add(marker);

    // Notify listeners
    if (_onMarkersUpdate != null) {
      _onMarkersUpdate!(_markers);
    }
  }

  /// Move the camera to follow the car
  Future<void> _moveCamera(LatLng position) async {
    if (_mapController != null && _mapController!.isCompleted) {
      final GoogleMapController controller = await _mapController!.future;
      controller.animateCamera(
        CameraUpdate.newLatLng(position),
      );
    }
  }

  /// Update the route when a new rider is added
  void updateRouteForNewRider(List<LatLng> newRoute) {
    // Keep track of the current location
    final currentLocation = _currentLocation;

    // Update the route
    _currentRoute = List.from(newRoute);

    // If we have a current location, update visited points
    if (currentLocation != null) {
      _updateVisitedPoints(currentLocation);
    }

    // Notify listeners
    if (_onRouteUpdate != null) {
      _onRouteUpdate!(_currentRoute);
    }

    logger.info(
        'Route updated for new rider with ${_currentRoute.length} points',
        tag: 'CarAnimation');
  }

  /// Get the remaining route points
  List<LatLng> getRemainingRoute() {
    return List.from(_currentRoute);
  }

  /// Get the visited points
  List<LatLng> getVisitedPoints() {
    return List.from(_visitedPoints);
  }

  /// Clean up resources
  void dispose() {
    _animationTimer?.cancel();
    _markers.clear();
    _visitedPoints.clear();
    _currentRoute.clear();
    _previousLocation = null;
    _currentLocation = null;
  }
}
