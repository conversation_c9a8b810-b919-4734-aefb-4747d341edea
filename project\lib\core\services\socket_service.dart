import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;
import '../config/api_config.dart';

class RideRequest {
  final String id;
  final Map<String, dynamic> pickupLocation;
  final Map<String, dynamic> destination;
  final String
      status; // 'pending', 'accepted', 'rejected', 'in_progress', 'completed'
  final String? driverId;
  final String riderId;
  final List<String>? sharedRiders;

  RideRequest({
    required this.id,
    required this.pickupLocation,
    required this.destination,
    required this.status,
    this.driverId,
    required this.riderId,
    this.sharedRiders,
  });

  factory RideRequest.fromJson(Map<String, dynamic> json) {
    List<String>? sharedRiders;
    if (json['sharedRiders'] != null) {
      sharedRiders = List<String>.from(json['sharedRiders']);
    }

    return RideRequest(
      id: json['id'],
      pickupLocation: json['pickupLocation'],
      destination: json['destination'],
      status: json['status'],
      driverId: json['driverId'],
      riderId: json['riderId'],
      sharedRiders: sharedRiders,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'pickupLocation': pickupLocation,
      'destination': destination,
      'status': status,
      'driverId': driverId,
      'riderId': riderId,
      'sharedRiders': sharedRiders,
    };
  }
}

class SharingRequest {
  final String rideId;
  final String newRiderId;
  final Map<String, dynamic> newPickupLocation;
  final Map<String, dynamic> newDestination;
  final Map<String, dynamic> prediction;

  SharingRequest({
    required this.rideId,
    required this.newRiderId,
    required this.newPickupLocation,
    required this.newDestination,
    required this.prediction,
  });

  factory SharingRequest.fromJson(Map<String, dynamic> json) {
    return SharingRequest(
      rideId: json['rideId'],
      newRiderId: json['newRiderId'],
      newPickupLocation: json['newPickupLocation'],
      newDestination: json['newDestination'],
      prediction: json['prediction'],
    );
  }
}

class SocketService {
  static final SocketService _instance = SocketService._internal();
  static SocketService get instance => _instance;

  io.Socket? socket;
  bool _isConnected = false;

  // Getter for connection status
  bool get isConnected => _isConnected;

  // Stream controllers for different events
  final _rideCreatedController = StreamController<String>.broadcast();
  final _rideAcceptedController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _rideRejectedController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _rideStartedController = StreamController<String>.broadcast();
  final _rideCompletedController = StreamController<String>.broadcast();
  final _newRideRequestController = StreamController<RideRequest>.broadcast();
  final _driverLocationController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _rideSharingRequestController =
      StreamController<SharingRequest>.broadcast();
  final _rideSharingResponseController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _connectionStatusController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _driverUnavailableController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _rideSharingAnalysisController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _rideSharedController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _rideStopAddedController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _noDriversAvailableController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _driverArrivedAtPickupController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _driverArrivedAtDestinationController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _rideErrorController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _rideNotificationController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _riderConfirmationController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _rideCoordinationController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _ridePhaseUpdateController =
      StreamController<Map<String, dynamic>>.broadcast();

  // Streams
  Stream<String> get onRideCreated => _rideCreatedController.stream;
  Stream<Map<String, dynamic>> get onRideAccepted =>
      _rideAcceptedController.stream;
  Stream<Map<String, dynamic>> get onRideRejected =>
      _rideRejectedController.stream;
  Stream<String> get onRideStarted => _rideStartedController.stream;
  Stream<String> get onRideCompleted => _rideCompletedController.stream;
  Stream<RideRequest> get onNewRideRequest => _newRideRequestController.stream;
  Stream<Map<String, dynamic>> get onDriverLocationUpdate =>
      _driverLocationController.stream;
  Stream<SharingRequest> get onRideSharingRequest =>
      _rideSharingRequestController.stream;
  Stream<Map<String, dynamic>> get onRideSharingResponse =>
      _rideSharingResponseController.stream;
  Stream<Map<String, dynamic>> get onDriverUnavailable =>
      _driverUnavailableController.stream;
  Stream<Map<String, dynamic>> get onNoDriversAvailable =>
      _noDriversAvailableController.stream;
  Stream<Map<String, dynamic>> get onDriverArrivedAtPickup =>
      _driverArrivedAtPickupController.stream;
  Stream<Map<String, dynamic>> get onDriverArrivedAtDestination =>
      _driverArrivedAtDestinationController.stream;
  Stream<Map<String, dynamic>> get onRideError => _rideErrorController.stream;
  Stream<Map<String, dynamic>> get onRideNotification =>
      _rideNotificationController.stream;
  Stream<Map<String, dynamic>> get onRiderConfirmation =>
      _riderConfirmationController.stream;
  Stream<Map<String, dynamic>> get onRideCoordination =>
      _rideCoordinationController.stream;
  Stream<Map<String, dynamic>> get onConnectionStatus =>
      _connectionStatusController.stream;
  Stream<Map<String, dynamic>> get onRidePhaseUpdate =>
      _ridePhaseUpdateController.stream;
  Stream<Map<String, dynamic>> get onRideSharingAnalysis =>
      _rideSharingAnalysisController.stream;
  Stream<Map<String, dynamic>> get onRideShared => _rideSharedController.stream;
  Stream<Map<String, dynamic>> get onRideStopAdded =>
      _rideStopAddedController.stream;

  SocketService._internal() {
    _initializeSocket();
  }

  void _initializeSocket() {
    socket = io.io(
      ApiConfig.baseUrl,
      io.OptionBuilder()
          .setTransports(['websocket'])
          .disableAutoConnect()
          .enableForceNew()
          .enableReconnection()
          .setReconnectionAttempts(5)
          .setReconnectionDelay(1000)
          .build(),
    );

    socket!.onConnect((_) {
      debugPrint('Socket Connected');
      _isConnected = true;
    });

    socket!.onDisconnect((_) {
      debugPrint('Socket Disconnected');
      _isConnected = false;
    });

    socket!.onConnectError((error) {
      debugPrint('Socket Connect Error: $error');
    });

    socket!.onError((error) {
      debugPrint('Socket Error: $error');
    });

    socket!.onReconnect((_) {
      debugPrint('Socket Reconnected');
    });

    // Set up event listeners
    socket!.on('ride:created', (data) {
      _rideCreatedController.add(data['rideId']);
    });

    socket!.on('ride:accepted', (data) {
      _rideAcceptedController.add(data);
    });

    socket!.on('ride:rejected', (data) {
      _rideRejectedController.add(data);
    });

    socket!.on('ride:started', (data) {
      _rideStartedController.add(data['rideId']);
    });

    socket!.on('ride:completed', (data) {
      _rideCompletedController.add(data['rideId']);
    });

    socket!.on('ride:new_request', (data) {
      _newRideRequestController.add(RideRequest.fromJson(data));
    });

    socket!.on('driver:location_update', (data) {
      _driverLocationController.add(data);
    });

    socket!.on('ride:sharing_request', (data) {
      _rideSharingRequestController.add(SharingRequest.fromJson(data));
    });

    socket!.on('ride:sharing_accepted', (data) {
      _rideSharingResponseController.add({
        'rideId': data['rideId'],
        'accepted': true,
      });
    });

    socket!.on('ride:sharing_rejected', (data) {
      _rideSharingResponseController.add({
        'rideId': data['rideId'],
        'accepted': false,
      });
    });

    socket!.on('ride:driver_unavailable', (data) {
      debugPrint('Driver unavailable: ${data['driverId']}');
      _driverUnavailableController.add(data);
    });

    socket!.on('ride:no_drivers_available', (data) {
      debugPrint('No drivers available: ${data['rideId']}');
      _noDriversAvailableController.add(data);
    });

    socket!.on('driver:arrived_at_pickup', (data) {
      debugPrint('Driver arrived at pickup: ${data['rideId']}');
      _driverArrivedAtPickupController.add(data);
    });

    socket!.on('driver:arrived_at_destination', (data) {
      debugPrint('Driver arrived at destination: ${data['rideId']}');
      _driverArrivedAtDestinationController.add(data);
    });

    socket!.on('ride:error', (data) {
      debugPrint('Ride error: ${data['error']} - ${data['message']}');
      _rideErrorController.add(data);
    });

    // إضافة مستمعين للإشعارات وتأكيدات الراكب
    socket!.on('ride:notification', (data) {
      debugPrint('Ride notification: ${data['type']} - ${data['message']}');
      _rideNotificationController.add(data);
    });

    socket!.on('ride:rider_confirmation', (data) {
      debugPrint(
          'Rider confirmation for ride: ${data['rideId']} - ${data['confirmed']}');
      _riderConfirmationController.add(data);
    });

    // إضافة مستمعين لأحداث تنسيق الرحلة
    socket!.on('ride:coordination_confirmed', (data) {
      debugPrint('Ride coordination confirmed: ${data['rideId']}');
      _rideCoordinationController.add(data);
    });

    socket!.on('ride:phase_update', (data) {
      debugPrint('Ride phase update: ${data['rideId']} - ${data['phase']}');
      _ridePhaseUpdateController.add(data);
    });

    // New events for multi-stop rides
    socket!.on('ride:sharing_analysis', (data) {
      debugPrint('Ride sharing analysis for ride: ${data['rideId']}');
      _rideSharingAnalysisController.add(data);
    });

    socket!.on('ride:shared', (data) {
      debugPrint(
          'Ride shared: ${data['rideId']} merged with ${data['mergedWithRideId']}');
      _rideSharedController.add(data);
    });

    socket!.on('ride:stop_added', (data) {
      debugPrint('Stop added to ride: ${data['rideId']}');
      _rideStopAddedController.add(data);
    });
  }

  void initialize() {
    if (socket == null) {
      socket = io.io(
        ApiConfig.baseUrl, // تأكد من إضافة هذا في ApiConfig
        io.OptionBuilder()
            .setTransports(['websocket'])
            .disableAutoConnect()
            .build(),
      );

      _initializeSocket();
    }
  }

  void connect() {
    initialize();
    if (!_isConnected) {
      debugPrint('Attempting to connect to socket at: ${ApiConfig.baseUrl}');
      socket?.connect();
      socket?.onConnect((_) {
        _isConnected = true;
        debugPrint('Socket connected successfully: ${socket?.id}');
        // Emit connection status
        _connectionStatusController
            .add({'connected': true, 'socketId': socket?.id});
      });

      socket?.onConnectError((error) {
        debugPrint('Socket connection error: $error');
        _isConnected = false;
        // Emit connection error
        _connectionStatusController
            .add({'connected': false, 'error': error.toString()});
      });

      socket?.onDisconnect((_) {
        _isConnected = false;
        debugPrint('Socket disconnected');
        // Emit disconnection status
        _connectionStatusController
            .add({'connected': false, 'reason': 'disconnected'});
      });
    }
  }

  void setDriverAvailable(String driverId) {
    if (_isConnected) {
      socket?.emit('driver:available', driverId);
    }
  }

  void setDriverUnavailable(String driverId) {
    if (_isConnected) {
      socket?.emit('driver:unavailable', driverId);
    }
  }

  void requestRide({
    required String riderId,
    required Map<String, dynamic> pickupLocation,
    required Map<String, dynamic> destination,
    String? specificDriverId,
  }) {
    if (_isConnected) {
      final Map<String, dynamic> requestData = {
        'riderId': riderId,
        'pickupLocation': pickupLocation,
        'destination': destination,
      };

      // إضافة معرف السائق المحدد إذا تم توفيره
      if (specificDriverId != null) {
        requestData['specificDriverId'] = specificDriverId;
      }

      socket?.emit('ride:request', requestData);

      debugPrint(
          'Ride request sent: ${specificDriverId != null ? 'To specific driver $specificDriverId' : 'To all available drivers'}');
    } else {
      debugPrint('Socket not connected. Cannot request ride.');
    }
  }

  void acceptRide({
    required String rideId,
    required String driverId,
  }) {
    if (_isConnected) {
      // Store driver ID for location updates
      setDriverId(driverId);

      // Emit ride acceptance event with enhanced data
      socket?.emit('ride:accept', {
        'rideId': rideId,
        'driverId': driverId,
        'timestamp': DateTime.now().toIso8601String(),
        'status': 'accepted',
        'action': 'ride_accepted_by_driver',
      });

      // Also emit a specific event for real-time coordination
      socket?.emit('ride:status_update', {
        'rideId': rideId,
        'driverId': driverId,
        'status': 'driver_accepted',
        'timestamp': DateTime.now().toIso8601String(),
        'message': 'Driver has accepted the ride and is preparing route',
      });

      debugPrint(
          'Driver $driverId accepted ride $rideId with enhanced coordination');
    } else {
      debugPrint('Socket not connected. Cannot accept ride.');
    }
  }

  void rejectRide({
    required String rideId,
    required String driverId,
  }) {
    if (_isConnected) {
      socket?.emit('ride:reject', {
        'rideId': rideId,
        'driverId': driverId,
      });
    }
  }

  void confirmRideSharing({
    required String rideId,
    required String driverId,
    required bool confirmed,
  }) {
    if (_isConnected) {
      socket?.emit('ride:confirm_sharing', {
        'rideId': rideId,
        'driverId': driverId,
        'confirmed': confirmed,
      });

      debugPrint(
          'Driver $driverId ${confirmed ? 'confirmed' : 'declined'} sharing ride $rideId');
    } else {
      debugPrint('Socket not connected. Cannot confirm ride sharing.');
    }
  }

  void startRide({
    required String rideId,
    Map<String, dynamic>? initialLocation,
    String? estimatedArrivalTime,
    bool riderConfirmed = false,
  }) {
    if (_isConnected) {
      final Map<String, dynamic> data = {
        'rideId': rideId,
        'timestamp': DateTime.now().toIso8601String(),
        'riderConfirmed': riderConfirmed,
        'status': 'in_progress',
      };

      // Add optional data if provided
      if (initialLocation != null) {
        data['initialLocation'] = initialLocation;
      }

      if (estimatedArrivalTime != null) {
        data['estimatedArrivalTime'] = estimatedArrivalTime;
      }

      // Add driver ID if available
      if (_driverId != null) {
        data['driverId'] = _driverId;
      }

      socket?.emit('ride:start', data);
      debugPrint(
          'Started ride $rideId with rider confirmation: $riderConfirmed');

      // إرسال إشعار للسائق والراكب ببدء الرحلة
      socket?.emit('ride:notification', {
        'rideId': rideId,
        'type': 'ride_started',
        'message': 'تم بدء الرحلة',
        'timestamp': DateTime.now().toIso8601String(),
      });
    } else {
      debugPrint('Socket not connected. Cannot start ride.');
    }
  }

  void completeRide({
    required String rideId,
    required String driverId,
  }) {
    if (_isConnected) {
      socket?.emit('ride:complete', {
        'rideId': rideId,
        'driverId': driverId,
      });
    }
  }

  // تخزين معرف السائق مؤقتًا
  String? _driverId;

  // تعيين معرف السائق
  void setDriverId(String driverId) {
    _driverId = driverId;
  }

  // الحصول على معرف السائق
  String? getDriverId() {
    return _driverId;
  }

  void updateDriverLocation({
    required String rideId,
    required Map<String, dynamic> location,
  }) {
    if (_isConnected) {
      if (_driverId == null) {
        debugPrint('Error: Driver ID is null. Cannot update location.');
        return;
      }

      socket?.emit('driver:location', {
        'rideId': rideId,
        'driverId': _driverId,
        'location': location,
      });

      debugPrint('Sent driver location update for ride $rideId: $location');
    } else {
      debugPrint('Socket not connected. Cannot update driver location.');
    }
  }

  void disconnect() {
    socket?.disconnect();
    _isConnected = false;
  }

  // دالة مساعدة للاستماع للأحداث
  StreamSubscription<dynamic> listenTo(
      String event, Function(dynamic) callback) {
    final controller = StreamController<dynamic>.broadcast();

    socket?.on(event, (data) {
      controller.add(data);
    });

    return controller.stream.listen(callback);
  }

  // إرسال تأكيد الراكب لبدء الرحلة
  void confirmRideStart({
    required String rideId,
    required String riderId,
  }) {
    if (_isConnected) {
      socket?.emit('ride:rider_confirmation', {
        'rideId': rideId,
        'riderId': riderId,
        'confirmed': true,
        'timestamp': DateTime.now().toIso8601String(),
      });

      debugPrint('Rider $riderId confirmed ride $rideId');

      // بدء الرحلة بعد تأكيد الراكب
      startRide(
        rideId: rideId,
        riderConfirmed: true,
      );
    } else {
      debugPrint('Socket not connected. Cannot confirm ride start.');
    }
  }

  void dispose() {
    _rideCreatedController.close();
    _rideAcceptedController.close();
    _rideRejectedController.close();
    _rideStartedController.close();
    _rideCompletedController.close();
    _newRideRequestController.close();
    _driverLocationController.close();
    _rideSharingRequestController.close();
    _rideSharingResponseController.close();
    _connectionStatusController.close();
    _driverUnavailableController.close();
    _noDriversAvailableController.close();
    _driverArrivedAtPickupController.close();
    _driverArrivedAtDestinationController.close();
    _rideErrorController.close();
    _rideNotificationController.close();
    _riderConfirmationController.close();
    disconnect();
  }
}
