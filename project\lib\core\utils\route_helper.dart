import 'dart:convert';
import 'dart:ui';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Route Helper
/// Provides utilities for route calculation and display
class RouteHelper {
  static const String _baseUrl = 'https://maps.googleapis.com/maps/api/directions/json';

  /// Get route between two points using Google Directions API
  static Future<RouteResult> getRoute({
    required LatLng origin,
    required LatLng destination,
    String mode = 'driving',
  }) async {
    try {
      final String apiKey = dotenv.env['GOOGLE_MAPS_API_KEY'] ?? '';
      if (apiKey.isEmpty) {
        throw Exception('Google Maps API key not found');
      }

      final String url = '$_baseUrl'
          '?origin=${origin.latitude},${origin.longitude}'
          '&destination=${destination.latitude},${destination.longitude}'
          '&mode=$mode'
          '&key=$apiKey';

      print('🗺️ Requesting route: $url');

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['status'] == 'OK' && data['routes'] != null && data['routes'].isNotEmpty) {
          final route = data['routes'][0];
          final leg = route['legs'][0];
          
          // Extract polyline points
          final encodedPolyline = route['overview_polyline']['points'];
          final points = _decodePolyline(encodedPolyline);

          // Extract route information
          final distance = leg['distance']['text'];
          final duration = leg['duration']['text'];
          final distanceValue = leg['distance']['value']; // in meters
          final durationValue = leg['duration']['value']; // in seconds

          print('✅ Route found: $distance, $duration, ${points.length} points');

          return RouteResult.success(
            points: points,
            distance: distance,
            duration: duration,
            distanceMeters: distanceValue,
            durationSeconds: durationValue,
          );
        } else {
          final errorMessage = data['error_message'] ?? 'No routes found';
          print('❌ Route error: ${data['status']} - $errorMessage');
          return RouteResult.failure('Route not found: $errorMessage');
        }
      } else {
        print('❌ HTTP Error: ${response.statusCode}');
        return RouteResult.failure('HTTP ${response.statusCode}: ${response.reasonPhrase}');
      }
    } catch (e) {
      print('❌ Route calculation error: $e');
      return RouteResult.failure('Route calculation failed: $e');
    }
  }

  /// Decode polyline string to list of LatLng points
  static List<LatLng> _decodePolyline(String encoded) {
    List<LatLng> points = [];
    int index = 0, len = encoded.length;
    int lat = 0, lng = 0;

    while (index < len) {
      int b, shift = 0, result = 0;
      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlat = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lat += dlat;

      shift = 0;
      result = 0;
      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlng = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lng += dlng;

      points.add(LatLng(lat / 1E5, lng / 1E5));
    }

    return points;
  }

  /// Create polyline from route points
  static Polyline createPolyline({
    required String polylineId,
    required List<LatLng> points,
    Color color = const Color(0xFF2196F3),
    int width = 5,
  }) {
    return Polyline(
      polylineId: PolylineId(polylineId),
      points: points,
      color: color,
      width: width,
      patterns: [], // Solid line
      geodesic: true,
    );
  }

  /// Create markers for origin and destination
  static Set<Marker> createRouteMarkers({
    required LatLng origin,
    required LatLng destination,
    String originTitle = 'Origin',
    String destinationTitle = 'Destination',
  }) {
    return {
      Marker(
        markerId: const MarkerId('origin'),
        position: origin,
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
        infoWindow: InfoWindow(title: originTitle),
      ),
      Marker(
        markerId: const MarkerId('destination'),
        position: destination,
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
        infoWindow: InfoWindow(title: destinationTitle),
      ),
    };
  }

  /// Calculate camera bounds for route
  static LatLngBounds calculateBounds(List<LatLng> points) {
    if (points.isEmpty) {
      throw ArgumentError('Points list cannot be empty');
    }

    double minLat = points.first.latitude;
    double maxLat = points.first.latitude;
    double minLng = points.first.longitude;
    double maxLng = points.first.longitude;

    for (final point in points) {
      minLat = minLat < point.latitude ? minLat : point.latitude;
      maxLat = maxLat > point.latitude ? maxLat : point.latitude;
      minLng = minLng < point.longitude ? minLng : point.longitude;
      maxLng = maxLng > point.longitude ? maxLng : point.longitude;
    }

    // Add padding
    const padding = 0.01;
    return LatLngBounds(
      southwest: LatLng(minLat - padding, minLng - padding),
      northeast: LatLng(maxLat + padding, maxLng + padding),
    );
  }
}

/// Route Result Class
class RouteResult {
  final bool isSuccess;
  final List<LatLng>? points;
  final String? distance;
  final String? duration;
  final int? distanceMeters;
  final int? durationSeconds;
  final String? errorMessage;

  RouteResult._({
    required this.isSuccess,
    this.points,
    this.distance,
    this.duration,
    this.distanceMeters,
    this.durationSeconds,
    this.errorMessage,
  });

  factory RouteResult.success({
    required List<LatLng> points,
    required String distance,
    required String duration,
    required int distanceMeters,
    required int durationSeconds,
  }) {
    return RouteResult._(
      isSuccess: true,
      points: points,
      distance: distance,
      duration: duration,
      distanceMeters: distanceMeters,
      durationSeconds: durationSeconds,
    );
  }

  factory RouteResult.failure(String errorMessage) {
    return RouteResult._(
      isSuccess: false,
      errorMessage: errorMessage,
    );
  }

  @override
  String toString() {
    if (isSuccess) {
      return 'RouteResult(success: $distance, $duration, ${points?.length} points)';
    } else {
      return 'RouteResult(error: $errorMessage)';
    }
  }
}
