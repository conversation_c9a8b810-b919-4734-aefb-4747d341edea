import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../domain/entities/payment_method.dart';
import '../providers/payment_provider.dart';
import '../widgets/payment_method_card.dart';
import '../widgets/add_payment_method_dialog.dart';
import '../../../../core/constants/my_colors.dart';

/// Payment Methods Screen
/// Displays and manages user's payment methods with clean architecture
class PaymentMethodsScreen extends StatefulWidget {
  final String userId;

  const PaymentMethodsScreen({
    Key? key,
    required this.userId,
  }) : super(key: key);

  @override
  State<PaymentMethodsScreen> createState() => _PaymentMethodsScreenState();
}

class _PaymentMethodsScreenState extends State<PaymentMethodsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<PaymentProvider>().loadPaymentMethods(widget.userId);
      context.read<PaymentProvider>().loadWallet(widget.userId);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MyColors.cBackgroundColor,
      appBar: AppBar(
        title: const Text(
          'Payment Methods',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add, color: Colors.white),
            onPressed: () => _showAddPaymentMethodDialog(),
          ),
        ],
      ),
      body: Consumer<PaymentProvider>(
        builder: (context, paymentProvider, child) {
          if (paymentProvider.isLoading) {
            return Center(
              child: CircularProgressIndicator(color: MyColors.primary),
            );
          }

          if (paymentProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.red.shade400,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: Text(
                      paymentProvider.error!,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      paymentProvider.clearError();
                      paymentProvider.loadPaymentMethods(widget.userId);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: MyColors.primary,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () => paymentProvider.loadPaymentMethods(widget.userId),
            color: MyColors.primary,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Wallet Section
                  if (paymentProvider.hasWallet) ...[
                    _buildWalletCard(paymentProvider.wallet!),
                    const SizedBox(height: 24),
                  ],

                  // Payment Methods Section
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Payment Methods',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      TextButton.icon(
                        onPressed: _showAddPaymentMethodDialog,
                        icon: Icon(Icons.add, color: MyColors.primary),
                        label: Text(
                          'Add New',
                          style: TextStyle(color: MyColors.primary),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Payment Methods List
                  if (paymentProvider.hasPaymentMethods) ...[
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: paymentProvider.paymentMethods.length,
                      separatorBuilder: (context, index) =>
                          const SizedBox(height: 12),
                      itemBuilder: (context, index) {
                        final paymentMethod =
                            paymentProvider.paymentMethods[index];
                        return PaymentMethodCard(
                          paymentMethod: paymentMethod,
                          isSelected:
                              paymentProvider.selectedPaymentMethod?.id ==
                                  paymentMethod.id,
                          onTap: () => paymentProvider
                              .selectPaymentMethod(paymentMethod),
                          onSetDefault: () =>
                              _setDefaultPaymentMethod(paymentMethod),
                          onDelete: () => _deletePaymentMethod(paymentMethod),
                        );
                      },
                    ),
                  ] else ...[
                    _buildEmptyState(),
                  ],

                  const SizedBox(height: 32),

                  // Test Payment Section (for development)
                  if (paymentProvider.selectedPaymentMethod != null) ...[
                    _buildTestPaymentSection(paymentProvider),
                  ],
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildWalletCard(Wallet wallet) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [MyColors.primary, Color(0xFF4A90E2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: MyColors.primary.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'My Wallet',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Icon(
                Icons.account_balance_wallet,
                color: Colors.white.withValues(alpha: 0.8),
                size: 24,
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            wallet.formattedBalance,
            style: const TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _showAddMoneyDialog(),
                  icon: const Icon(Icons.add, size: 18),
                  label: const Text('Add Money'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: MyColors.primary,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _showWalletTransactions(),
                  icon: const Icon(Icons.history, size: 18),
                  label: const Text('History'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.white,
                    side: const BorderSide(color: Colors.white),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.payment,
            size: 64,
            color: Colors.grey.shade600,
          ),
          const SizedBox(height: 16),
          Text(
            'No Payment Methods',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade400,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add a payment method to get started',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showAddPaymentMethodDialog,
            icon: const Icon(Icons.add),
            label: const Text('Add Payment Method'),
            style: ElevatedButton.styleFrom(
              backgroundColor: MyColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestPaymentSection(PaymentProvider paymentProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade900,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade700),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Test Payment (Development)',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _testPayment(true),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Test Success'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _testPayment(false),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Test Failure'),
                ),
              ),
            ],
          ),
          if (paymentProvider.lastSimulation != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: paymentProvider.lastSimulation!.success
                    ? Colors.green.withValues(alpha: 0.2)
                    : Colors.red.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Last Simulation Result:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: paymentProvider.lastSimulation!.success
                          ? Colors.green
                          : Colors.red,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    paymentProvider.lastSimulation!.success
                        ? 'Payment Successful'
                        : 'Payment Failed: ${paymentProvider.lastSimulation!.errorMessage}',
                    style: const TextStyle(color: Colors.white70),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _showAddPaymentMethodDialog() {
    showDialog(
      context: context,
      builder: (context) => AddPaymentMethodDialog(userId: widget.userId),
    );
  }

  void _showAddMoneyDialog() {
    // Implementation for add money dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add money feature coming soon!')),
    );
  }

  void _showWalletTransactions() {
    // Implementation for wallet transactions screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Wallet transactions feature coming soon!')),
    );
  }

  void _setDefaultPaymentMethod(PaymentMethod paymentMethod) {
    context
        .read<PaymentProvider>()
        .setDefaultPaymentMethod(widget.userId, paymentMethod.id);
  }

  void _deletePaymentMethod(PaymentMethod paymentMethod) {
    // Implementation for delete payment method
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
          content: Text('Delete payment method feature coming soon!')),
    );
  }

  void _testPayment(bool shouldSucceed) {
    context.read<PaymentProvider>().simulatePayment(
          amount: 25.0,
          currency: 'SAR',
          shouldSucceed: shouldSucceed,
        );
  }
}
