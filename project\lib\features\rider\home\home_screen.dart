import 'dart:async';
import 'dart:convert';
import 'dart:isolate';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:geocoding/geocoding.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:project/core/providers/map_state_provider.dart';
import 'package:project/core/providers/ride_state_provider.dart';
import 'package:project/core/providers/suggestion_provider.dart';
import 'package:project/core/services/socket_service.dart';
import 'package:project/core/services/ride_service.dart';
import 'package:project/core/services/voice_assistant_service.dart';
import 'package:project/features/driver/views/nearby_drivers_screen.dart';
import 'package:project/features/suggestions/screens/suggestions_screen.dart';

import 'package:project/features/rider/widget/address_popup.dart';
import 'package:project/features/auth/views/login/notification_page.dart';
import 'package:project/core/widgets/language_selector.dart';
import 'package:project/core/services/ride_coordination_service.dart';
import 'package:project/core/widgets/unified_ride_status_widget.dart';
import 'package:project/core/widgets/ai_status_indicator.dart';
import 'package:project/core/providers/ai_ride_provider.dart';
import 'package:project/core/theme/app_theme.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../generated/l10n/app_localizations.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final Completer<GoogleMapController> _mapController = Completer();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  LatLng? _currentPosition;
  final LatLng _defaultPosition = const LatLng(37.7749, -122.4194);
  Set<Marker> _markers = {};
  List<LatLng> _polylinePoints = [];
  Set<Polyline> _polylines = {};
  bool _isMapInitialized = false;
  LatLng? _carPosition;
  List<LatLng> _routePoints = [];
  late TextEditingController toController;
  bool _rideCompleted = false;
  final RideService _rideService = RideService.instance;
  final VoiceAssistantService _voiceAssistantService = VoiceAssistantService();

  // Ride state variables
  String? _currentRideId;
  String? _currentRideStatus;
  String? _currentDriverId;
  Map<String, dynamic>? _currentDriverInfo;
  bool _isSharedRide = false;
  // Moved _activeSubscriptions to the top to avoid undefined reference
  final List<Function()> _activeSubscriptions = [];

  @override
  void initState() {
    super.initState();
    toController = TextEditingController();

    // استخدام مزود حالة الخريطة للحصول على الموقع الحالي
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final mapProvider = Provider.of<MapStateProvider>(context, listen: false);
      mapProvider.getCurrentLocation().then((_) {
        // تحديث الموقع الحالي من مزود الحالة
        setState(() {
          _currentPosition = mapProvider.currentPosition;
          if (_currentPosition != null) {
            _markers.add(
              Marker(
                markerId: const MarkerId('current_location'),
                position: _currentPosition!,
                icon: BitmapDescriptor.defaultMarkerWithHue(
                    BitmapDescriptor.hueBlue),
              ),
            );
          }
        });

        // تحميل السائقين القريبين
        mapProvider.loadNearbyDrivers(radius: 5.0).then((_) {
          setState(() {
            _markers.addAll(mapProvider.markers);
          });
        });

        // تحميل الاقتراحات المخصصة بعد تحديد الموقع
        _loadPersonalizedSuggestions();
      });
    });

    _isMapInitialized = true;

    // Connect to Socket.IO server
    SocketService.instance.connect();

    // Listen for ride sharing opportunities
    _listenForRideSharingOpportunities();

    // تهيئة المساعد الصوتي
    _initializeVoiceAssistant();

    // تهيئة خدمة الذكاء الاصطناعي
    _initializeAIService();
  }

  /// تهيئة المساعد الصوتي
  Future<void> _initializeVoiceAssistant() async {
    await _voiceAssistantService.initialize();
  }

  /// تهيئة خدمة الذكاء الاصطناعي
  Future<void> _initializeAIService() async {
    try {
      final aiProvider = Provider.of<AIRideProvider>(context, listen: false);
      await aiProvider.initializeAIService();
      debugPrint('AI Service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing AI service: $e');
    }
  }

  // تم استبدال _loadNearbyDrivers بالاستخدام المباشر لـ MapStateProvider

  // Mock drivers function removed

  /// Listen for ride sharing opportunities
  void _listenForRideSharingOpportunities() {
    final sharingOpportunitySubscription = SocketService.instance.listenTo(
      'ride:sharing_opportunity',
      (data) {
        if (!mounted) return;

        debugPrint('Received ride sharing opportunity: $data');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('هناك رحلة متاحة للمشاركة بالقرب منك!'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 5),
              action: SnackBarAction(
                label: 'عرض',
                onPressed: () {
                  _showRideSharingOpportunityDialog(data);
                },
              ),
            ),
          );
        }
      },
    );

    _activeSubscriptions.add(() {
      sharingOpportunitySubscription.cancel();
    });
  }

  /// Show ride sharing opportunity dialog
  void _showRideSharingOpportunityDialog(dynamic data) {
    if (!mounted) return;

    final activeRideId = data['activeRideId'];
    final pickupLocation = data['pickupLocation'];
    final destination = data['destination'];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.share, color: Colors.green),
            SizedBox(width: 8),
            Text('فرصة مشاركة رحلة'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
                'هناك رحلة متاحة للمشاركة بالقرب منك. هل ترغب في مشاركة هذه الرحلة؟'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(Icons.location_on, color: Colors.green, size: 20),
                      SizedBox(width: 8),
                      Text('نقطة الالتقاط:',
                          style: TextStyle(fontWeight: FontWeight.bold)),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                      '${pickupLocation['lat'].toStringAsFixed(4)}, ${pickupLocation['lng'].toStringAsFixed(4)}'),
                  const SizedBox(height: 8),
                  const Row(
                    children: [
                      Icon(Icons.location_pin, color: Colors.red, size: 20),
                      SizedBox(width: 8),
                      Text('الوجهة:',
                          style: TextStyle(fontWeight: FontWeight.bold)),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                      '${destination['lat'].toStringAsFixed(4)}, ${destination['lng'].toStringAsFixed(4)}'),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _requestRideSharing(activeRideId);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('طلب المشاركة'),
          ),
        ],
      ),
    );
  }

  /// Request ride sharing
  void _requestRideSharing(String activeRideId) async {
    if (!mounted) return;

    try {
      _showLoadingDialog('جاري إرسال طلب مشاركة الرحلة...');

      final riderId = await _getUserId();

      if (_currentPosition == null) {
        if (mounted) {
          Navigator.of(context, rootNavigator: true).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لم يتم العثور على موقعك الحالي'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      SocketService.instance.socket?.emit('ride:sharing_request', {
        'rideId': activeRideId,
        'newRiderId': riderId,
        'newPickupLocation': {
          'lat': _currentPosition!.latitude,
          'lng': _currentPosition!.longitude,
        },
        'newDestination': _markers.isNotEmpty
            ? {
                'lat': _markers.first.position.latitude,
                'lng': _markers.first.position.longitude,
              }
            : {
                'lat': _currentPosition!.latitude + 0.01,
                'lng': _currentPosition!.longitude + 0.01,
              },
      });

      late final StreamSubscription responseSubscription;

      responseSubscription = SocketService.instance.listenTo(
        'ride:sharing_requested',
        (response) {
          if (mounted) {
            try {
              Navigator.of(context, rootNavigator: true).pop();
            } catch (_) {}
          }

          if (response['success'] == true) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إرسال طلب مشاركة الرحلة بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
              _showPredictionDetailsDialog(response['prediction']);
            }
          } else {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      'فشل في إرسال طلب مشاركة الرحلة: ${response['message']}'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }

          responseSubscription.cancel();
        },
      );

      _activeSubscriptions.add(() {
        responseSubscription.cancel();
      });
    } catch (e) {
      debugPrint('خطأ في طلب مشاركة الرحلة: $e');

      if (mounted) {
        try {
          Navigator.of(context, rootNavigator: true).pop();
        } catch (_) {}
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في طلب مشاركة الرحلة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Get user ID
  Future<String> _getUserId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('user_id');

      if (userId != null && userId.isNotEmpty) {
        return userId;
      } else {
        final newUserId = 'user_${DateTime.now().millisecondsSinceEpoch}';
        await prefs.setString('user_id', newUserId);
        return newUserId;
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على معرف المستخدم: $e');
      return 'user_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// Show loading dialog
  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PopScope(
        canPop: false,
        child: AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(message),
            ],
          ),
        ),
      ),
    );
  }

  /// Show prediction details dialog
  void _showPredictionDetailsDialog(dynamic prediction) {
    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.analytics, color: Colors.blue),
            SizedBox(width: 8),
            Text('تحليل الذكاء الاصطناعي'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('تحليل الذكاء الاصطناعي لطلب مشاركة الرحلة:'),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          prediction['shouldAddRider']
                              ? Icons.check_circle
                              : Icons.cancel,
                          color: prediction['shouldAddRider']
                              ? Colors.green
                              : Colors.red,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          prediction['shouldAddRider']
                              ? 'مشاركة الرحلة مفيدة'
                              : 'مشاركة الرحلة غير مفيدة',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                        'درجة الثقة: ${(prediction['score'] * 100).toStringAsFixed(1)}%'),
                    Text('كفاءة المشاركة: ${prediction['efficiency']}%'),
                    if (prediction['fareDetails'] != null) ...[
                      const SizedBox(height: 16),
                      const Text('تفاصيل التكلفة:',
                          style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      Text(
                          'التوفير الإجمالي: ${prediction['fareDetails']['totalSavings']} ج.م'),
                      Text(
                          'توفير الراكب الأصلي: ${prediction['fareDetails']['originalRider']['savings']} ج.م'),
                      Text(
                          'توفير الراكب الجديد: ${prediction['fareDetails']['newRider']['savings']} ج.م'),
                    ],
                    if (prediction['environmentalImpact'] != null) ...[
                      const SizedBox(height: 16),
                      const Text('التأثير البيئي:',
                          style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      Text(
                          'تقليل انبعاثات CO2: ${prediction['environmentalImpact']['co2Reduction']} كجم'),
                      Text(
                          'توفير الوقود: ${prediction['environmentalImpact']['fuelSaved']} لتر'),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    toController.dispose();
    for (final disposeFunction in _activeSubscriptions) {
      disposeFunction();
    }
    _activeSubscriptions.clear();
    super.dispose();
  }

  /// تحميل الاقتراحات المخصصة
  Future<void> _loadPersonalizedSuggestions() async {
    try {
      // الحصول على معرف المستخدم
      final userId = await _getUserId();

      if (!mounted) return;

      // الحصول على مزود الاقتراحات
      final suggestionProvider =
          Provider.of<SuggestionProvider>(context, listen: false);

      // إنشاء اقتراحات مخصصة للمستخدم
      await suggestionProvider.generatePersonalizedSuggestions(userId);

      if (!mounted) return;

      // تحميل اقتراحات بناءً على الموقع الحالي
      if (_currentPosition != null) {
        await suggestionProvider.loadSuggestionsForLocation(
          _currentPosition!,
          userId: userId,
        );
      }

      if (!mounted) return;

      debugPrint(
          'تم تحميل ${suggestionProvider.filteredSuggestions.length} اقتراحات');
    } catch (e) {
      debugPrint('خطأ في تحميل الاقتراحات المخصصة: $e');
    }
  }

  /// تحميل وعرض الاقتراحات
  Future<void> _loadAndShowSuggestions() async {
    try {
      final suggestionProvider =
          Provider.of<SuggestionProvider>(context, listen: false);

      // تحميل الاقتراحات بناءً على الموقع الحالي
      if (_currentPosition != null) {
        // الحصول على معرف المستخدم
        final userId = await _getUserId();

        await suggestionProvider.loadSuggestionsForLocation(
          _currentPosition!,
          userId: userId,
        );

        // فتح شاشة الاقتراحات
        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const SuggestionsScreen(),
            ),
          );
        }
      } else {
        // إذا لم يكن الموقع الحالي متاحًا، اطلب من المستخدم تحديد موقعه
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content:
                  Text('Please enable location services to get suggestions'),
              duration: Duration(seconds: 3),
            ),
          );
          _getCurrentLocation();
        }
      }
    } catch (e) {
      debugPrint('Error loading suggestions: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading suggestions: $e')),
        );
      }
    }
  }

  /// بدء المساعد الصوتي
  Future<void> _startVoiceAssistant() async {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري الاستماع...'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );

    // استخدام خدمة المساعد الصوتي للاستماع
    final command = await _voiceAssistantService.listen();

    if (command != null && command.isNotEmpty) {
      // معالجة الأمر باستخدام خدمة الذكاء الاصطناعي
      await _voiceAssistantService.processCommand(command);

      // تنفيذ الإجراءات المناسبة بناءً على الأمر
      if (mounted) {
        if (command.contains('طلب رحلة')) {
          _showAddressPopup(context);
        } else if (command.contains('أين السائق')) {
          // عرض موقع السائق
          _checkDriverLocation();
        } else if (command.contains('إلغاء الرحلة')) {
          // إلغاء الرحلة الحالية
          _cancelRide();
        } else if (command.contains('تأكيد الرحلة') ||
            command.contains('بدء الرحلة')) {
          // تأكيد بدء الرحلة
          _confirmRideStart();
        } else if (command.contains('مشاركة الرحلة')) {
          // مشاركة الرحلة
          _showRideSharingOptions();
        } else if (command.contains('الصفحة الرئيسية')) {
          // الانتقال إلى الصفحة الرئيسية
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('أنت بالفعل في الصفحة الرئيسية'),
              backgroundColor: Colors.blue,
            ),
          );
        }
      }
    }
  }

  /// التحقق من موقع السائق
  Future<void> _checkDriverLocation() async {
    try {
      // التحقق مما إذا كانت هناك رحلة نشطة
      final hasActiveRide = _markers.any((m) => m.markerId.value == 'driver');

      if (!hasActiveRide) {
        await _voiceAssistantService.speak('ليس لديك رحلة نشطة حالياً.');
        return;
      }

      // يمكن استخدام المعلومات المتاحة لتحديد حالة الرحلة
      if (_carPosition != null) {
        await _voiceAssistantService
            .speak('السائق في الطريق إليك، سيصل قريباً.');

        // تحريك الكاميرا إلى موقع السائق
        if (_mapController.isCompleted) {
          final controller = await _mapController.future;
          controller.animateCamera(CameraUpdate.newLatLng(_carPosition!));
        }
      } else {
        await _voiceAssistantService
            .speak('جاري البحث عن سائق. يرجى الانتظار...');
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من موقع السائق: $e');
      await _voiceAssistantService
          .speak('حدث خطأ أثناء التحقق من موقع السائق.');
    }
  }

  /// تأكيد بدء الرحلة
  Future<void> _confirmRideStart() async {
    try {
      // التحقق مما إذا كانت هناك رحلة نشطة
      final hasActiveRide = _markers.any((m) => m.markerId.value == 'driver');

      if (!hasActiveRide) {
        await _voiceAssistantService.speak('ليس لديك رحلة نشطة لبدئها.');
        return;
      }

      // محاكاة تأكيد بدء الرحلة
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تأكيد بدء الرحلة'),
            backgroundColor: Colors.green,
          ),
        );
      }

      await _voiceAssistantService.speak('تم تأكيد بدء الرحلة. رحلة سعيدة!');
    } catch (e) {
      debugPrint('خطأ في تأكيد بدء الرحلة: $e');
      await _voiceAssistantService.speak('حدث خطأ أثناء تأكيد بدء الرحلة.');
    }
  }

  /// عرض خيارات مشاركة الرحلة
  void _showRideSharingOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.share),
            title: const Text('مشاركة تفاصيل الرحلة'),
            onTap: () {
              Navigator.pop(context);
              _voiceAssistantService.speak('جاري مشاركة تفاصيل الرحلة...');
              // تنفيذ مشاركة تفاصيل الرحلة
            },
          ),
          ListTile(
            leading: const Icon(Icons.group_add),
            title: const Text('البحث عن فرص مشاركة الرحلة'),
            onTap: () {
              Navigator.pop(context);
              _voiceAssistantService
                  .speak('جاري البحث عن فرص مشاركة الرحلة...');
              // البحث عن فرص مشاركة الرحلة
            },
          ),
        ],
      ),
    );
  }

  /// طلب رحلة جديدة
  Future<void> _requestRide() async {
    try {
      await _voiceAssistantService.speak('جاري طلب رحلة جديدة...');

      // هنا يمكن إضافة الكود الخاص بطلب الرحلة
      // على سبيل المثال، يمكن استخدام _rideService.requestRide()

      // في هذا المثال، سنقوم بمحاكاة طلب الرحلة
      await Future.delayed(const Duration(seconds: 2));

      await _voiceAssistantService
          .speak('تم طلب الرحلة بنجاح. جاري البحث عن سائق...');

      // إضافة علامة السائق على الخريطة (محاكاة)
      final driverPosition = LatLng(
        _currentPosition!.latitude + 0.01,
        _currentPosition!.longitude + 0.01,
      );

      setState(() {
        _markers.add(
          Marker(
            markerId: const MarkerId('driver'),
            position: driverPosition,
            icon: BitmapDescriptor.defaultMarkerWithHue(
                BitmapDescriptor.hueAzure),
            infoWindow: const InfoWindow(
              title: 'السائق',
              snippet: 'في الطريق إليك',
            ),
          ),
        );

        _carPosition = driverPosition;
      });
    } catch (e) {
      debugPrint('خطأ في طلب الرحلة: $e');
      await _voiceAssistantService.speak('حدث خطأ أثناء طلب الرحلة.');
    }
  }

  /// عرض تأكيد إلغاء الرحلة
  void _showCancelRideConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء الرحلة'),
        content: const Text('هل أنت متأكد من رغبتك في إلغاء الرحلة الحالية؟'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _voiceAssistantService.speak('تم إلغاء عملية الإلغاء.');
            },
            child: const Text('لا'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _cancelRide();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('نعم، إلغاء الرحلة'),
          ),
        ],
      ),
    );
  }

  /// إلغاء الرحلة
  Future<void> _cancelRide() async {
    try {
      // التحقق مما إذا كانت هناك رحلة نشطة
      final hasActiveRide = _markers.any((m) => m.markerId.value == 'driver');

      if (!hasActiveRide) {
        await _voiceAssistantService.speak('ليس لديك رحلة نشطة لإلغائها.');
        return;
      }

      // محاكاة إلغاء الرحلة
      await Future.delayed(const Duration(seconds: 1));

      // إزالة علامة السائق وخط المسار
      setState(() {
        _markers.removeWhere((m) => m.markerId.value == 'driver');
        _polylines.clear();
        _carPosition = null;
      });

      await _voiceAssistantService.speak('تم إلغاء الرحلة بنجاح.');
    } catch (e) {
      debugPrint('خطأ في إلغاء الرحلة: $e');
      await _voiceAssistantService.speak('حدث خطأ أثناء إلغاء الرحلة.');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      drawer: const Drawer(),
      body: _buildFlutterMapPage(),
      // floatingActionButton: Column(
      //   mainAxisAlignment: MainAxisAlignment.end,
      //   children: [
      //     // زر الاقتراحات
      //     FloatingActionButton(
      //       heroTag: 'suggestions',
      //       onPressed: () {
      //         _loadAndShowSuggestions();
      //       },
      //       backgroundColor: Colors.amber,
      //       child: const Icon(Icons.lightbulb),
      //     ),
      //     const SizedBox(height: 16),
      //     // زر المساعد الصوتي
      //     FloatingActionButton(
      //       heroTag: 'voice_assistant',
      //       onPressed: () {
      //         _showVoiceAssistantOptions();
      //       },
      //       backgroundColor: Colors.blue,
      //       child: const Icon(Icons.mic),
      //     ),
      //     const SizedBox(height: 16),
      //     // زر تحديد الموقع الحالي
      //     FloatingActionButton(
      //       heroTag: 'current_location',
      //       onPressed: () {
      //         _getCurrentLocation();
      //       },
      //       backgroundColor: Colors.green,
      //       child: const Icon(Icons.my_location),
      //     ),
      //   ],
      // ),

      // // زر الأوامر الصوتية الجديد
      // // يتم وضعه في الزاوية اليسرى السفلية
      // bottomNavigationBar: Padding(
      // padding: const EdgeInsets.only(left: 16.0, bottom: 16.0),
      // child: Align(
      // alignment: Alignment.bottomLeft,
      // child: VoiceCommandButton(
      //   position: VoiceButtonPosition.bottomLeft,
      //   onSetDestination: (location, address) {
      //     // تعيين الوجهة
      //     if (_currentPosition != null) {
      //       _drawRouteAndAnimate(_currentPosition!, location);
      //     }
      //   },
      //   onRequestRide: () {
      //     // طلب رحلة
      //     _requestRide();
      //   },
      //   onCheckStatus: () {
      //     // التحقق من حالة الرحلة
      //     _checkDriverLocation();
      //   },
      //   onCancelRide: () {
      //     // إلغاء الرحلة
      //     _showCancelRideConfirmation();
      //   },
      //   onNavigateToHome: () {
      //     // بالفعل في الصفحة الرئيسية
      //     ScaffoldMessenger.of(context).showSnackBar(
      //       const SnackBar(
      //         content: Text('أنت بالفعل في الصفحة الرئيسية'),
      //         backgroundColor: Colors.blue,
      //       ),
      //     );
      //   },
      //   onNavigateBack: () {
      //     // العودة للخلف
      //     Navigator.of(context).maybePop();
      //   },
      // ),
      // ),
      // ),
    );
  }

  // تم استبدال _checkAndRequestPermission بالاستخدام المباشر لـ MapStateProvider

  List<LatLng> _decodePolyline(String encoded) {
    List<LatLng> points = [];
    int index = 0, len = encoded.length;
    int lat = 0, lng = 0;

    while (index < len) {
      int b, shift = 0, result = 0;
      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlat = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lat += dlat;

      shift = 0;
      result = 0;
      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlng = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lng += dlng;

      points.add(LatLng(lat / 1E5, lng / 1E5));
    }
    debugPrint('Decoded ${points.length} points from polyline');
    return points;
  }

  Future<void> _drawRouteAndAnimate(LatLng start, LatLng end) async {
    if (!_isMapInitialized) {
      debugPrint('Map not initialized yet');
      return;
    }

    try {
      debugPrint('Drawing route from $start to $end');

      // استخدام مزود حالة الخريطة لرسم المسار
      final mapProvider = Provider.of<MapStateProvider>(context, listen: false);
      final rideProvider =
          Provider.of<RideStateProvider>(context, listen: false);

      // تحديث حالة الرحلة
      rideProvider.updatePickupAndDestination(start, end);

      // استخدام Google Maps Directions API بدلاً من OpenStreetMap
      final String apiKey = dotenv.env['GOOGLE_MAPS_API_KEY'] ?? '';
      final String url = 'https://maps.googleapis.com/maps/api/directions/json'
          '?origin=${start.latitude},${start.longitude}'
          '&destination=${end.latitude},${end.longitude}'
          '&mode=driving'
          '&key=$apiKey';

      final response = await http.get(Uri.parse(url));
      debugPrint(
          'Google Directions API Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['code'] != 'Ok') {
          throw Exception(
              'OSRM API error: ${data['code']} - ${data['message'] ?? 'No error message'}');
        }

        if (data['routes'] != null && data['routes'].isNotEmpty) {
          final route = data['routes'][0];
          final encodedPolyline = route['geometry'];
          final points = _decodePolyline(encodedPolyline);

          // تحديث المسار في مزود حالة الخريطة
          mapProvider.setRoutePoints(points);
          mapProvider.clearPolylines();
          mapProvider.addPolyline(
            Polyline(
              polylineId: const PolylineId('route'),
              points: points,
              color: Colors.blue,
              width: 4,
            ),
          );

          // تحديث المسار المحلي أيضاً
          setState(() {
            _routePoints = points;
            _polylinePoints = points;
            _polylines.clear();
            _polylines.add(
              Polyline(
                polylineId: const PolylineId('route'),
                points: points,
                color: Colors.blue,
                width: 4,
              ),
            );
          });

          if (_mapController.isCompleted) {
            final controller = await _mapController.future;
            controller.animateCamera(
              CameraUpdate.newLatLngBounds(
                LatLngBounds(
                  southwest: LatLng(
                    min(start.latitude, end.latitude) - 0.05,
                    min(start.longitude, end.longitude) - 0.05,
                  ),
                  northeast: LatLng(
                    max(start.latitude, end.latitude) + 0.05,
                    max(start.longitude, end.longitude) + 0.05,
                  ),
                ),
                100,
              ),
            );
          }

          _startCarAnimation();
        } else {
          throw Exception('No routes found');
        }
      } else {
        throw Exception(
            'HTTP ${response.statusCode}: ${response.reasonPhrase}');
      }
    } catch (e) {
      debugPrint('Error drawing route: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to draw route: $e')),
        );
      }
    }
  }

  void _startCarAnimation() async {
    if (_routePoints.isEmpty) return;

    final receivePort = ReceivePort();
    await Isolate.spawn(
      _animateCarIsolate,
      {
        'sendPort': receivePort.sendPort,
        'routePoints': _routePoints
            .map((point) => {'lat': point.latitude, 'lng': point.longitude})
            .toList(),
      },
    );

    receivePort.listen((message) {
      if (message is int && message < _routePoints.length) {
        setState(() {
          _carPosition = _routePoints[message];
          _markers.removeWhere((m) => m.markerId.value == 'car');
          _markers.add(
            Marker(
              markerId: const MarkerId('car'),
              position: _carPosition!,
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueAzure),
            ),
          );

          if (_mapController.isCompleted) {
            _mapController.future.then((controller) {
              controller.animateCamera(CameraUpdate.newLatLng(_carPosition!));
            });
          }
        });
      } else if (message == _routePoints.length) {
        setState(() {
          _rideCompleted = true;
        });
        receivePort.close();
      }
    });
  }

  static void _animateCarIsolate(Map<String, dynamic> params) async {
    final SendPort sendPort = params['sendPort'];
    final List<Map<String, dynamic>> routePointsData = params['routePoints'];
    final List<Map<String, dynamic>> routePoints = routePointsData;

    int currentIndex = 0;
    while (true) {
      await Future.delayed(const Duration(milliseconds: 500));
      if (currentIndex < routePoints.length) {
        sendPort.send(currentIndex);
        currentIndex++;
      } else {
        sendPort.send(routePoints.length);
        break;
      }
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      // استخدام مزود حالة الخريطة للحصول على الموقع الحالي
      final mapProvider = Provider.of<MapStateProvider>(context, listen: false);
      await mapProvider.getCurrentLocation();

      setState(() {
        _currentPosition = mapProvider.currentPosition;
        debugPrint('Current location: $_currentPosition');
      });

      if (_currentPosition != null &&
          _isMapInitialized &&
          _mapController.isCompleted) {
        final controller = await _mapController.future;
        controller
            .animateCamera(CameraUpdate.newLatLngZoom(_currentPosition!, 14.0));
      }
    } catch (e) {
      debugPrint('Error getting location: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error getting location: $e')),
        );
      }
    }
  }

  void _onMapTap(LatLng position) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );
      if (placemarks.isNotEmpty) {
        Placemark placemark = placemarks.first;
        String address =
            "${placemark.name ?? ''}, ${placemark.locality ?? ''}, ${placemark.country ?? ''}";
        setState(() {
          _markers.clear();
          _markers.add(
            Marker(
              markerId: const MarkerId('selected_location'),
              position: position,
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueBlue),
            ),
          );
        });
        if (mounted) {
          _showAddressPopup(context, toAddress: address, toPosition: position);
        }
      }
    } catch (e) {
      debugPrint('Error reverse geocoding: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error reverse geocoding: $e')),
        );
      }
    }
  }

  Future<void> _searchLocation(String address) async {
    try {
      // استخدام مزود حالة الخريطة للبحث عن الموقع
      final mapProvider = Provider.of<MapStateProvider>(context, listen: false);
      await mapProvider.searchLocation(address);

      // تحديث العلامات المحلية إذا كان مزود الحالة لديه علامات
      if (mapProvider.markers.isNotEmpty) {
        setState(() {
          _markers.clear();
          _markers.addAll(mapProvider.markers);
        });
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Location not found')),
        );
      }
    } catch (e) {
      debugPrint('Error searching location: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error searching location: $e')),
        );
      }
    }
  }

  void _refreshMap() {
    setState(() {
      _markers.clear();
      _polylinePoints.clear();
      _routePoints.clear();
      _polylines.clear();
      _carPosition = null;
      _rideCompleted = false;
      toController.clear();
      if (_currentPosition != null) {
        _markers.add(
          Marker(
            markerId: const MarkerId('current_location'),
            position: _currentPosition!,
            icon:
                BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
          ),
        );
        if (_isMapInitialized && _mapController.isCompleted) {
          _mapController.future.then((controller) {
            controller.animateCamera(
                CameraUpdate.newLatLngZoom(_currentPosition!, 14.0));
          });
        }
      }
    });
  }

  Widget _buildFlutterMapPage() {
    // استخدام مزود حالة الخريطة
    final mapProvider = Provider.of<MapStateProvider>(context);

    return Stack(
      children: [
        // خريطة Google مع تأثيرات حديثة
        AnimatedContainer(
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(0),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 15,
                spreadRadius: 3,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(0),
            child: GoogleMap(
              initialCameraPosition: CameraPosition(
                target: mapProvider.currentPosition ?? _defaultPosition,
                zoom: 16.0,
              ),
              myLocationEnabled: true,
              myLocationButtonEnabled: false,
              markers:
                  mapProvider.markers.isEmpty ? _markers : mapProvider.markers,
              polylines: mapProvider.polylines.isEmpty
                  ? _polylines
                  : mapProvider.polylines,
              onMapCreated: (GoogleMapController controller) {
                _mapController.complete(controller);
                mapProvider.initializeMap(controller);
                setState(() {
                  _isMapInitialized = true;
                });
              },
              onTap: _onMapTap,
            ),
          ),
        ),
        // Modern AI-Enhanced Top Bar
        Positioned(
          top: 40,
          left: 16,
          right: 16,
          child: _buildModernTopBar(),
        ),
        // Enhanced Action Panel
        // Positioned(
        //   top: 100,
        //   right: 16,
        //   child: _buildEnhancedActionPanel(),
        // ),

        // Quick Service Access Panel
        // Positioned(
        //   bottom: 200,
        //   left: 16,
        //   right: 16,
        //   child: _buildQuickServicePanel(),
        // ),
        // Positioned(
        //   top: 100,
        //   left: 20,
        //   right: 20,
        //   child: Container(
        //     padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        //     decoration: BoxDecoration(
        //       color: Colors.white,
        //       borderRadius: BorderRadius.circular(8),
        //       boxShadow: [
        //         BoxShadow(
        //           color: Colors.black.withOpacity(0.1),
        //           blurRadius: 4,
        //           offset: const Offset(0, 2),
        //         ),
        //       ],
        //     ),
        //     child: TextField(
        //       decoration: const InputDecoration(
        //         hintText: 'Search for places...',
        //         border: InputBorder.none,
        //         prefixIcon: Icon(Icons.search),
        //       ),
        //       onSubmitted: (value) {
        //         if (value.isNotEmpty) {
        //           _searchLocation(value);
        //         }
        //       },
        //     ),
        //   ),
        // ),
        // Positioned(
        //   bottom: 80,
        //   left: 20,
        //   right: 20,
        //   child: _buildTransportSelector(),
        // ),
        Positioned(
          bottom: 100,
          left: 20,
          right: 20,
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: toController,
                  decoration: InputDecoration(
                    fillColor: Colors.white70,
                    filled: true,
                    prefixIcon: const Icon(Icons.location_on_outlined),
                    labelText: AppLocalizations.of(context).whereWouldYouGo,
                    border: const OutlineInputBorder(),
                  ),
                  onChanged: (value) => setState(() {}),
                  onSubmitted: (value) {
                    if (toController.text.isNotEmpty) {
                      _searchLocation(toController.text);
                    }
                  },
                ),
              ),
              const SizedBox(width: 8),
              CircleAvatar(
                radius: 24,
                backgroundColor: Colors.white70,
                child: IconButton(
                  onPressed: _getCurrentLocation,
                  icon: const Icon(
                    Icons.location_on,
                    color: Colors.black,
                    size: 30,
                  ),
                ),
              ),
            ],
          ),
        ),
        if (_rideCompleted)
          Positioned(
            bottom: 40,
            left: 20,
            right: 20,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: _refreshMap,
              child: Text(
                AppLocalizations.of(context).refreshAndStartOver,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ),
      ],
    );
  }

  void _showAddressPopup(
    BuildContext context, {
    String? toAddress,
    LatLng? toPosition,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => AddressPopup(
        currentPosition: _currentPosition,
        onSearch: _searchLocation,
        onRouteConfirmed: _drawRouteAndAnimate,
        onRideRequested: _sendRideRequest,
        initialToAddress: toAddress,
        initialToPosition: toPosition,
      ),
    );
  }

  // تم إزالة الدالة _buildTransportSelector لأنها غير مستخدمة

  Future<void> _sendRideRequest(LatLng destination) async {
    try {
      if (!mounted) return;

      // عرض قائمة السائقين المتاحين للاختيار
      await _showAvailableDriversDialog(destination);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في إرسال طلب الرحلة: $e')),
        );
      }
    }
  }

  Future<void> _showAvailableDriversDialog(LatLng destination) async {
    try {
      // عرض مؤشر تحميل أثناء جلب السائقين المتاحين
      _showLoadingDialog('جاري البحث عن السائقين المتاحين...');

      // الحصول على معرف المستخدم
      final userId = await _getUserId();

      // إنشاء الرحلة
      final ride = await _rideService.createRide(
        riderId: userId,
        pickupLocation: {
          'lat': _currentPosition?.latitude ?? 0,
          'lng': _currentPosition?.longitude ?? 0,
        },
        destination: {
          'lat': destination.latitude,
          'lng': destination.longitude,
        },
      );

      debugPrint('Ride created successfully with ID: ${ride.id}');

      // محاكاة جلب السائقين المتاحين (في التطبيق الحقيقي، يجب استدعاء API لجلب السائقين المتاحين)
      await Future.delayed(const Duration(seconds: 1));

      // إغلاق مؤشر التحميل
      if (mounted) {
        Navigator.of(context, rootNavigator: true).pop();
      }

      if (!mounted) return;

      // إنشاء قائمة وهمية من السائقين المتاحين
      final List<Map<String, dynamic>> availableDrivers = [
        {
          'id': 'driver_1',
          'name': 'أحمد محمد',
          'rating': 4.8,
          'car': 'تويوتا كورولا',
          'plate': 'ABC 123',
          'distance': '3 كم',
          'eta': '5 دقائق',
          'price': '25 ج.م',
        },
        {
          'id': 'driver_2',
          'name': 'محمود علي',
          'rating': 4.5,
          'car': 'هيونداي إلنترا',
          'plate': 'XYZ 789',
          'distance': '4 كم',
          'eta': '7 دقائق',
          'price': '23 ج.م',
        },
        {
          'id': 'driver_3',
          'name': 'خالد إبراهيم',
          'rating': 4.9,
          'car': 'نيسان صني',
          'plate': 'DEF 456',
          'distance': '2 كم',
          'eta': '3 دقائق',
          'price': '28 ج.م',
        },
      ];

      // عرض قائمة السائقين المتاحين مع تحسينات الذكاء الاصطناعي
      if (mounted) {
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) => _buildEnhancedDriverSelectionModal(
            availableDrivers,
            ride,
            destination,
          ),
        );
      }

      // إعداد مستمعي الرحلة
      _setupRideListeners(ride.id);
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحًا
      if (mounted) {
        try {
          Navigator.of(context, rootNavigator: true).pop();
        } catch (_) {}
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في البحث عن السائقين: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }

      rethrow;
    }
  }

  Future<void> _requestSpecificDriver(
      String rideId, String driverId, LatLng destination) async {
    try {
      if (!mounted) return;

      // عرض شاشة الانتظار
      _showWaitingForDriverPopup(context);

      // الحصول على معرف المستخدم
      final userId = await _getUserId();

      // إرسال طلب الرحلة إلى السائق المحدد
      SocketService.instance.requestRide(
        riderId: userId,
        pickupLocation: {
          'lat': _currentPosition?.latitude ?? 0,
          'lng': _currentPosition?.longitude ?? 0,
        },
        destination: {
          'lat': destination.latitude,
          'lng': destination.longitude,
        },
        specificDriverId: driverId,
      );
    } catch (e) {
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في إرسال طلب الرحلة: $e')),
        );
      }
    }
  }

  void _setupRideListeners(String? rideId) {
    final acceptSubscription =
        SocketService.instance.onRideAccepted.listen((data) {
      if (mounted) {
        debugPrint('Ride accepted event received: $data');

        // إلغاء شاشة الانتظار إذا كانت مفتوحة
        try {
          Navigator.of(context, rootNavigator: true).pop();
        } catch (e) {
          debugPrint('Error closing waiting dialog: $e');
        }

        // عرض رسالة تأكيد قبول الطلب
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم قبول طلب الرحلة! جاري تحضير الرحلة...'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );

        // معالجة قبول الرحلة
        _handleRideAccepted(data);
      }
    });

    // Listen for shared ride events
    final sharedRideSubscription =
        SocketService.instance.onRideShared.listen((data) {
      if (mounted) {
        debugPrint('Ride shared event received: $data');

        // إلغاء شاشة الانتظار إذا كانت مفتوحة
        try {
          Navigator.of(context, rootNavigator: true).pop();
        } catch (e) {
          debugPrint('Error closing waiting dialog: $e');
        }

        // عرض رسالة تأكيد مشاركة الرحلة
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content:
                Text('سيتم مشاركة رحلتك مع راكب آخر! جاري تحضير الرحلة...'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );

        // معالجة مشاركة الرحلة
        _handleRideShared(data);
      }
    });

    final rejectSubscription =
        SocketService.instance.onRideRejected.listen((data) {
      if (mounted) {
        Navigator.pop(context);
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تم رفض الطلب'),
            content: const Text(
                'للأسف، تم رفض طلب الرحلة من قبل السائق. يمكنك المحاولة مرة أخرى أو تجربة خيارات أخرى.'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: const Text('حسناً'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  if (_markers.isNotEmpty) {
                    _sendRideRequest(_markers.first.position);
                  }
                },
                style: TextButton.styleFrom(foregroundColor: Colors.green),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        );
      }
    });

    final driverUnavailableSubscription =
        SocketService.instance.onDriverUnavailable.listen((data) {
      if (mounted) {
        debugPrint('Driver unavailable: $data');
        try {
          Navigator.pop(context); // إغلاق شاشة الانتظار
        } catch (e) {
          debugPrint('Error closing dialog: $e');
        }
        _handleDriverUnavailable(data);
      }
    });

    final noDriversAvailableSubscription =
        SocketService.instance.onNoDriversAvailable.listen((data) {
      if (mounted) {
        debugPrint('No drivers available: $data');
        try {
          Navigator.pop(context); // إغلاق شاشة الانتظار
        } catch (e) {
          debugPrint('Error closing dialog: $e');
        }
        _handleNoDriversAvailable(data);
      }
    });

    final driverArrivedAtPickupSubscription =
        SocketService.instance.onDriverArrivedAtPickup.listen((data) {
      if (mounted) {
        debugPrint('Driver arrived at pickup: $data');
        _handleDriverArrivedAtPickup(data);
      }
    });

    final driverArrivedAtDestinationSubscription =
        SocketService.instance.onDriverArrivedAtDestination.listen((data) {
      if (mounted) {
        debugPrint('Driver arrived at destination: $data');
        _handleDriverArrivedAtDestination(data);
      }
    });

    final rideErrorSubscription =
        SocketService.instance.onRideError.listen((data) {
      if (mounted) {
        debugPrint('Ride error: $data');
        _handleRideError(data);
      }
    });

    final rideStartedSubscription =
        SocketService.instance.onRideStarted.listen((rideId) {
      if (mounted) {
        debugPrint('Ride started event received: $rideId');

        // عرض رسالة بدء الرحلة
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('بدأت الرحلة! السائق في طريقه إلى الوجهة...'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );

        // Create ride data map from rideId
        final rideData = {
          'rideId': rideId,
          'driverId': 'unknown',
          'riderId': 'unknown',
          'pickupLocation': {'lat': 0.0, 'lng': 0.0},
          'destination': {'lat': 0.0, 'lng': 0.0},
        };

        // معالجة بدء الرحلة
        _handleRideStarted(rideData);

        // إظهار واجهة الرحلة الموحدة للراكب
        _showRiderRideInterface(rideData);
      }
    });

    Timer? timeoutTimer;
    Timer? extendedTimeoutTimer;

    if (rideId != null) {
      // Phase 1: Initial 3-minute search
      timeoutTimer = Timer(const Duration(minutes: 3), () async {
        try {
          if (!mounted) return;

          final ride = await _rideService.getRideById(rideId);

          if (ride.status == 'pending' && mounted) {
            // Phase 2: Extended search

            // Show extended waiting dialog
            if (mounted) {
              try {
                Navigator.of(context, rootNavigator: true).pop();
              } catch (e) {
                debugPrint('Error closing dialog: $e');
              }
            }

            if (mounted) {
              _showExtendedWaitingDialog(rideId);
            }

            // Phase 2: Extended 2-minute search
            extendedTimeoutTimer = Timer(const Duration(minutes: 2), () async {
              try {
                if (!mounted) return;

                final rideCheck = await _rideService.getRideById(rideId);

                if (rideCheck.status == 'pending' && mounted) {
                  await _rideService
                      .updateRide(rideId, {'status': 'cancelled'});

                  if (mounted) {
                    try {
                      Navigator.of(context, rootNavigator: true).pop();
                    } catch (e) {
                      debugPrint('Error closing dialog: $e');
                    }
                  }

                  if (mounted) {
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: const Text('انتهت مهلة الانتظار'),
                        content: const Text(
                            'لم يتم العثور على سائق متاح خلال الوقت المحدد. يرجى المحاولة مرة أخرى لاحقًا.'),
                        actions: [
                          TextButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            child: const Text('حسناً'),
                          ),
                        ],
                      ),
                    );
                  }
                }
              } catch (e) {
                debugPrint(
                    'Error checking ride status in extended timeout: $e');
              }
            });
          }
        } catch (e) {
          debugPrint('Error checking ride status: $e');
        }
      });
    }

    void disposeListeners() {
      acceptSubscription.cancel();
      rejectSubscription.cancel();
      driverUnavailableSubscription.cancel();
      noDriversAvailableSubscription.cancel();
      driverArrivedAtPickupSubscription.cancel();
      driverArrivedAtDestinationSubscription.cancel();
      rideErrorSubscription.cancel();
      rideStartedSubscription.cancel();
      sharedRideSubscription.cancel();
      timeoutTimer?.cancel();
      extendedTimeoutTimer?.cancel();
    }

    _activeSubscriptions.add(disposeListeners);
  }

  void _showExtendedWaitingDialog(String rideId) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PopScope(
        canPop: false,
        child: Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Container(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Extended search icon
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.orange.shade100,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.search,
                    size: 40,
                    color: Colors.orange.shade600,
                  ),
                ),
                const SizedBox(height: 20),

                // Title
                Text(
                  'البحث الموسع عن سائق',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.orange.shade700,
                      ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),

                // Description
                Text(
                  'نحن نبحث في منطقة أوسع للعثور على سائق متاح لك. قد يستغرق هذا دقيقتين إضافيتين.',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),

                // Progress indicator
                LinearProgressIndicator(
                  backgroundColor: Colors.orange.shade100,
                  valueColor:
                      AlwaysStoppedAnimation<Color>(Colors.orange.shade600),
                ),
                const SizedBox(height: 20),

                // Tips
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.lightbulb,
                              color: Colors.blue.shade600, size: 20),
                          const SizedBox(width: 8),
                          Text(
                            'نصائح للحصول على رحلة أسرع:',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '• جرب تغيير موقع الانطلاق قليلاً\n• تحقق من أوقات الذروة في منطقتك\n• فكر في استخدام وسائل النقل البديلة',
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.blue.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),

                // Cancel button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () async {
                      try {
                        await _rideService
                            .updateRide(rideId, {'status': 'cancelled'});
                        if (mounted) {
                          Navigator.of(context).pop();
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('تم إلغاء طلب الرحلة'),
                              backgroundColor: Colors.orange,
                            ),
                          );
                        }
                      } catch (e) {
                        debugPrint('Error cancelling ride: $e');
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('إلغاء الطلب'),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showWaitingForDriverPopup(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PopScope(
        canPop: false,
        child: Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 10),
                Container(
                  padding: const EdgeInsets.all(15),
                  decoration: BoxDecoration(
                    color: Colors.green
                        .withValues(alpha: 26, red: 76, green: 175, blue: 80),
                    shape: BoxShape.circle,
                  ),
                  child: const CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
                    strokeWidth: 3,
                  ),
                ),
                const SizedBox(height: 20),
                const Text(
                  'انتظار السائق',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 15),
                const Text(
                  'جاري البحث عن سائقين متاحين...',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 10),
                StreamBuilder<int>(
                  stream: Stream.periodic(const Duration(seconds: 1), (i) => i),
                  builder: (context, snapshot) {
                    final seconds = snapshot.data ?? 0;
                    return Text(
                      'وقت الانتظار: $seconds ثانية',
                      style: const TextStyle(fontSize: 14, color: Colors.grey),
                    );
                  },
                ),
                const SizedBox(height: 25),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('تم إلغاء طلب الرحلة')),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 30, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  child:
                      const Text('إلغاء الطلب', style: TextStyle(fontSize: 16)),
                ),
                const SizedBox(height: 10),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /*
  Future<void> _startRide({
    required LatLng driverPosition,
    required LatLng pickupLocation,
    required LatLng destination,
    required String rideId,
    required String driverId,
  }) async {
    setState(() {
      _markers.clear();
      _polylines.clear();
      _markers.add(
        Marker(
          markerId: const MarkerId('driver'),
          position: driverPosition,
          icon:
              BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure),
        ),
      );
      _markers.add(
        Marker(
          markerId: const MarkerId('pickup'),
          position: pickupLocation,
          icon:
              BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
        ),
      );
      _markers.add(
        Marker(
          markerId: const MarkerId('destination'),
          position: destination,
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
        ),
      );
    });

    await _drawRouteAndAnimate(driverPosition, pickupLocation);

    _listenForDriverLocationUpdates(driverId);

    Future.delayed(const Duration(seconds: 10), () {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('وصل السائق إلى نقطة الالتقاء')),
        );
        _drawRouteAndAnimate(pickupLocation, destination);
        _rideService.updateRide(rideId, {'status': 'in_progress'});
      }
    });
  }
  */

  void _startSimpleRide() {
    if (_currentPosition != null && _markers.isNotEmpty) {
      _drawRouteAndAnimate(_currentPosition!, _markers.first.position);
    }
  }

  /*
  /// بدء الرحلة تلقائيًا في الخلفية بدون تغيير واجهة المستخدم
  void _startRideInBackground({
    required LatLng driverPosition,
    required LatLng pickupLocation,
    required LatLng destination,
    required String rideId,
    required String driverId,
  }) {
    debugPrint(
        'Starting ride in background: rideId=$rideId, driverId=$driverId');

    // تحديث حالة الرحلة في الخادم
    _rideService.updateRide(rideId, {'status': 'accepted'});

    // بدء الاستماع لتحديثات موقع السائق
    _listenForDriverLocationUpdates(driverId);

    // إعداد المؤقت لتحديث حالة الرحلة إلى "in_progress" بعد وصول السائق إلى نقطة الالتقاط
    Future.delayed(const Duration(seconds: 10), () {
      if (mounted) {
        debugPrint('Driver arrived at pickup location');
        _rideService.updateRide(rideId, {'status': 'in_progress'});
      }
    });
  }
  */

  void _listenForDriverLocationUpdates(String driverId) {
    SocketService.instance.onDriverLocationUpdate.listen((data) {
      if (data['driverId'] == driverId && mounted) {
        try {
          final lat = data['location']['lat'];
          final lng = data['location']['lng'];
          final phase = data['phase'] as String?; // 'pickup' أو 'destination'
          final progress = data['progress'] as Map<String, dynamic>?;

          debugPrint(
              'Driver location update: lat=$lat, lng=$lng, phase=$phase, progress=$progress');

          setState(() {
            _carPosition = LatLng(lat, lng);
            _markers.removeWhere((m) => m.markerId.value == 'driver');
            _markers.add(
              Marker(
                markerId: const MarkerId('driver'),
                position: _carPosition!,
                icon: BitmapDescriptor.defaultMarkerWithHue(
                    BitmapDescriptor.hueAzure),
                infoWindow: InfoWindow(
                  title: 'السائق',
                  snippet: phase == 'pickup'
                      ? 'في الطريق إليك'
                      : 'في الطريق إلى الوجهة',
                ),
              ),
            );

            // تحديث شريط التقدم إذا كانت معلومات التقدم متوفرة
            if (progress != null) {
              final percentage = progress['percentage'] as int? ?? 0;
              _updateProgressIndicator(percentage, phase ?? 'pickup');
            }
          });

          // تحريك الكاميرا لمتابعة السائق
          if (_mapController.isCompleted) {
            _mapController.future.then((controller) {
              controller.animateCamera(CameraUpdate.newLatLng(_carPosition!));
            });
          }
        } catch (e) {
          debugPrint('Error updating driver location: $e');
        }
      }
    });
  }

  void _updateProgressIndicator(int percentage, String phase) {
    // يمكن تنفيذ هذه الوظيفة لعرض شريط تقدم الرحلة في واجهة المستخدم
    // على سبيل المثال، يمكن تحديث متغير حالة يعرض نسبة التقدم

    // هذا مجرد مثال، يمكن تعديله حسب احتياجات التطبيق
    if (mounted) {
      ScaffoldMessenger.of(context).clearSnackBars();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            phase == 'pickup'
                ? 'السائق في الطريق إليك: $percentage%'
                : 'في الطريق إلى الوجهة: $percentage%',
          ),
          duration: const Duration(seconds: 1),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }

  void _handleDriverUnavailable(Map<String, dynamic> data) {
    final String driverId = data['driverId'] ?? '';
    final String message = data['message'] ?? 'السائق المحدد غير متاح حاليًا';

    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              Icon(Icons.error_outline, color: Colors.orange.shade700),
              const SizedBox(width: 10),
              const Text('السائق غير متاح'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(message),
              const SizedBox(height: 10),
              Text('معرف السائق: $driverId',
                  style: const TextStyle(fontSize: 14, color: Colors.grey)),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // إعادة فتح قائمة السائقين المتاحين
                if (_markers.isNotEmpty) {
                  _showAvailableDriversDialog(_markers.first.position);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('اختيار سائق آخر'),
            ),
          ],
        ),
      );
    }
  }

  void _handleNoDriversAvailable(Map<String, dynamic> data) {
    final String message = data['message'] ??
        'لا يوجد سائقين متاحين حاليًا، يرجى المحاولة مرة أخرى لاحقًا';

    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              Icon(Icons.error_outline, color: Colors.red.shade700),
              const SizedBox(width: 10),
              const Text('لا يوجد سائقين متاحين'),
            ],
          ),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('حسناً'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // إعادة المحاولة بعد فترة
                if (_markers.isNotEmpty) {
                  _sendRideRequest(_markers.first.position);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }
  }

  void _handleDriverArrivedAtPickup(Map<String, dynamic> data) {
    final String message = data['message'] ?? 'وصل السائق إلى نقطة الالتقاط';

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'حسناً',
            textColor: Colors.white,
            onPressed: () {},
          ),
        ),
      );
    }
  }

  void _handleDriverArrivedAtDestination(Map<String, dynamic> data) {
    final String message = data['message'] ?? 'وصل السائق إلى الوجهة';
    final location = data['location'] != null
        ? LatLng(
            data['location']['lat'],
            data['location']['lng'],
          )
        : null;

    if (mounted) {
      // تحديث موقع السائق على الخريطة إذا كان متوفرًا
      if (location != null) {
        setState(() {
          _carPosition = location;
          _markers.removeWhere((m) => m.markerId.value == 'driver');
          _markers.add(
            Marker(
              markerId: const MarkerId('driver'),
              position: _carPosition!,
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueAzure),
              infoWindow: const InfoWindow(
                title: 'السائق',
                snippet: 'وصل إلى الوجهة',
              ),
            ),
          );
        });
      }

      // عرض رسالة تأكيد وصول السائق إلى الوجهة
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green),
              SizedBox(width: 10),
              Text('اكتملت الرحلة'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(message),
              const SizedBox(height: 20),
              const Text('شكراً لاستخدامك تطبيقنا!'),
            ],
          ),
          actions: [
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);

                // إعادة تعيين الخريطة
                setState(() {
                  _markers.clear();
                  _polylines.clear();
                  _carPosition = null;
                });

                // عرض زر طلب رحلة جديدة
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text('هل ترغب في طلب رحلة جديدة؟'),
                    action: SnackBarAction(
                      label: 'نعم',
                      onPressed: () {
                        // إعادة تعيين الخريطة وعرض واجهة طلب رحلة جديدة
                        _resetMapAndShowRideRequest();
                      },
                    ),
                    duration: const Duration(seconds: 10),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('حسناً'),
            ),
          ],
        ),
      );
    }
  }

  void _resetMapAndShowRideRequest() {
    setState(() {
      _markers.clear();
      _polylines.clear();
      _carPosition = null;
      // إعادة تعيين أي متغيرات حالة أخرى إذا لزم الأمر
    });

    // إعادة تعيين الكاميرا إلى الموقع الحالي
    if (_currentPosition != null && _mapController.isCompleted) {
      _mapController.future.then((controller) {
        controller.animateCamera(CameraUpdate.newLatLngZoom(
          _currentPosition!,
          15,
        ));
      });
    }
  }

  /// إظهار واجهة الرحلة الموحدة للراكب
  void _showRiderRideInterface(Map<String, dynamic> rideData) async {
    try {
      // Initialize coordination service
      await RideCoordinationService.instance.initialize();

      // Start coordinated ride for rider
      await RideCoordinationService.instance.startCoordinatedRide(
        rideId: rideData['rideId'] ?? 'unknown',
        driverId: rideData['driverId'] ?? 'unknown',
        riderId: rideData['riderId'] ?? 'unknown',
        pickupLocation: LatLng(
          rideData['pickupLocation']?['lat'] ??
              _currentPosition?.latitude ??
              0.0,
          rideData['pickupLocation']?['lng'] ??
              _currentPosition?.longitude ??
              0.0,
        ),
        destination: LatLng(
          rideData['destination']?['lat'] ?? 0.0,
          rideData['destination']?['lng'] ?? 0.0,
        ),
        isDriver: false, // This is for the rider
      );

      // Show unified ride interface
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => Dialog(
            insetPadding: const EdgeInsets.all(16),
            child: UnifiedRideStatusWidget(
              isDriver: false, // This is for the rider
              onCancelRide: () {
                RideCoordinationService.instance.cancelRide('إلغاء من الراكب');
                Navigator.of(context).pop();
                // Reset UI state
                setState(() {
                  _markers.clear();
                  _polylines.clear();
                });
              },
              onContactOther: () {
                // Implement contact driver functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('ميزة الاتصال بالسائق قريباً')),
                );
              },
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint('خطأ في إظهار واجهة الرحلة للراكب: $e');
    }
  }

  void _handleRideError(Map<String, dynamic> data) {
    final String message = data['message'] ?? 'حدث خطأ غير معروف';

    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.error, color: Colors.red),
              SizedBox(width: 10),
              Text('خطأ'),
            ],
          ),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('حسناً'),
            ),
          ],
        ),
      );
    }
  }

  void _handleRideStarted(dynamic data) {
    // التحقق من نوع البيانات
    Map<String, dynamic> rideData;
    if (data is String) {
      // إذا كانت البيانات عبارة عن معرف الرحلة فقط
      rideData = {'rideId': data};
    } else if (data is Map<String, dynamic>) {
      // إذا كانت البيانات عبارة عن خريطة
      rideData = data;
    } else {
      debugPrint('Invalid ride started data type: ${data.runtimeType}');
      return;
    }

    final String rideId = rideData['rideId'] ?? '';
    final String driverId = rideData['driverId'] ?? '';

    // محاولة استخراج إحداثيات نقطة الالتقاط والوجهة
    LatLng? pickupLocation;
    LatLng? destination;

    if (rideData['pickupLocation'] != null) {
      try {
        pickupLocation = LatLng(
          rideData['pickupLocation']['lat'],
          rideData['pickupLocation']['lng'],
        );
      } catch (e) {
        debugPrint('Error parsing pickup location: $e');
      }
    }

    if (rideData['destination'] != null) {
      try {
        destination = LatLng(
          rideData['destination']['lat'],
          rideData['destination']['lng'],
        );
      } catch (e) {
        debugPrint('Error parsing destination: $e');
      }
    }

    debugPrint('Handling ride started: rideId=$rideId, driverId=$driverId');

    if (mounted) {
      // إذا كانت معلومات المسار متوفرة، استخدمها لرسم المسار
      if (pickupLocation != null && destination != null) {
        setState(() {
          // إضافة علامات نقطة الالتقاط والوجهة إذا لم تكن موجودة
          _markers.removeWhere((m) => m.markerId.value == 'pickup');
          _markers.removeWhere((m) => m.markerId.value == 'destination');

          _markers.add(
            Marker(
              markerId: const MarkerId('pickup'),
              position: pickupLocation!, // استخدام ! لتأكيد أن القيمة ليست null
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueGreen),
            ),
          );

          _markers.add(
            Marker(
              markerId: const MarkerId('destination'),
              position: destination!, // استخدام ! لتأكيد أن القيمة ليست null
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueRed),
            ),
          );
        });

        // رسم المسار من نقطة الالتقاط إلى الوجهة
        _drawRouteAndAnimate(pickupLocation, destination);
      } else {
        // إذا لم تكن إحداثيات المسار متوفرة، حاول الحصول عليها من الخادم
        _rideService.getRideById(rideId).then((ride) {
          if (mounted) {
            final pickupLatLng = LatLng(
              ride.pickupLocation['lat'],
              ride.pickupLocation['lng'],
            );

            final destinationLatLng = LatLng(
              ride.destination['lat'],
              ride.destination['lng'],
            );

            setState(() {
              _markers.removeWhere((m) => m.markerId.value == 'pickup');
              _markers.removeWhere((m) => m.markerId.value == 'destination');

              _markers.add(
                Marker(
                  markerId: const MarkerId('pickup'),
                  position: pickupLatLng,
                  icon: BitmapDescriptor.defaultMarkerWithHue(
                      BitmapDescriptor.hueGreen),
                ),
              );

              _markers.add(
                Marker(
                  markerId: const MarkerId('destination'),
                  position: destinationLatLng,
                  icon: BitmapDescriptor.defaultMarkerWithHue(
                      BitmapDescriptor.hueRed),
                ),
              );
            });

            // رسم المسار من نقطة الالتقاط إلى الوجهة
            _drawRouteAndAnimate(pickupLatLng, destinationLatLng);
          }
        }).catchError((error) {
          debugPrint('Error fetching ride details: $error');
        });
      }
    }
  }

  Future<void> _handleRideAccepted(Map<String, dynamic> data) async {
    final String rideId = data['rideId'] ?? '';
    final String driverId = data['driverId'] ?? '';
    final String riderId = data['riderId'] ?? '';
    final timestamp = data['timestamp'] ?? DateTime.now().toIso8601String();

    debugPrint(
        'Processing ride acceptance: rideId=$rideId, driverId=$driverId, riderId=$riderId, timestamp=$timestamp');

    try {
      // عرض مؤشر تحميل معلومات الرحلة
      _showLoadingDialog('جاري تحميل معلومات الرحلة...');

      // الحصول على تفاصيل الرحلة من الخادم
      final ride = await _rideService.getRideById(rideId);
      debugPrint('Ride details retrieved: ${ride.id}, status=${ride.status}');

      // تحديد موقع السائق من البيانات المرسلة من الخادم أو استخدام موقع افتراضي
      final driverPosition = data['driverLocation'] != null
          ? LatLng(
              data['driverLocation']['lat'],
              data['driverLocation']['lng'],
            )
          : LatLng(
              ride.pickupLocation['lat'] - 0.005,
              ride.pickupLocation['lng'] - 0.005,
            );

      debugPrint(
          'Driver position set to: ${driverPosition.latitude}, ${driverPosition.longitude}');

      // إغلاق مؤشر التحميل
      if (mounted) {
        try {
          Navigator.of(context, rootNavigator: true).pop();
        } catch (e) {
          debugPrint('Error closing loading dialog: $e');
        }
      }

      // بدء الرحلة تلقائيًا في الخلفية مع نظام التنسيق الموحد
      _showRiderRideInterface({
        'rideId': rideId,
        'driverId': driverId,
        'riderId': riderId,
        'pickupLocation': ride.pickupLocation,
        'destination': ride.destination,
        'driverInfo': data['driverInfo'],
        'driverLocation': data['driverLocation'],
      });

      if (mounted) {
        // عرض تأكيد قبول الرحلة مع معلومات السائق
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            child: Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // أيقونة النجاح
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.green
                          .withValues(alpha: 40, red: 76, green: 175, blue: 80),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 50,
                    ),
                  ),
                  const SizedBox(height: 20),

                  // عنوان التأكيد
                  const Text(
                    'تم قبول الرحلة!',
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 15),

                  // رسالة التأكيد
                  const Text(
                    'قبل سائق طلب رحلتك. ستبدأ رحلتك الآن.',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 20),

                  // معلومات السائق
                  Container(
                    padding: const EdgeInsets.all(15),
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(
                          alpha: 30, red: 158, green: 158, blue: 158),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        // اسم السائق
                        Row(
                          children: [
                            const Icon(Icons.person,
                                color: Colors.blue, size: 20),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                data['driverInfo']?['name'] ?? 'سائق',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),

                        // تقييم السائق
                        Row(
                          children: [
                            const Icon(Icons.star,
                                color: Colors.amber, size: 20),
                            const SizedBox(width: 8),
                            Text('${data['driverInfo']?['rating'] ?? '4.5'} ★'),
                          ],
                        ),
                        const SizedBox(height: 8),

                        // معلومات السيارة
                        Row(
                          children: [
                            const Icon(Icons.directions_car,
                                color: Colors.green, size: 20),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                '${data['driverInfo']?['car'] ?? 'سيارة'} • ${data['driverInfo']?['plate'] ?? 'لوحة'}',
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),

                        // وقت الوصول المتوقع
                        Row(
                          children: [
                            const Icon(Icons.access_time,
                                color: Colors.orange, size: 20),
                            const SizedBox(width: 8),
                            Text(
                                'الوقت المقدر للوصول: ${data['driverInfo']?['eta'] ?? '5 دقائق'}'),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 25),

                  // زر بدء الرحلة
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        // بدء الرحلة مع نظام التنسيق الموحد
                        _showRiderRideInterface({
                          'rideId': rideId,
                          'driverId': driverId,
                          'riderId': riderId,
                          'pickupLocation': ride.pickupLocation,
                          'destination': ride.destination,
                          'driverInfo': data['driverInfo'],
                          'driverLocation': data['driverLocation'],
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      child: const Text('متابعة الرحلة',
                          style: TextStyle(fontSize: 16)),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint('خطأ في معالجة قبول الرحلة: $e');

      // إغلاق مؤشر التحميل إذا كان مفتوحًا
      if (mounted) {
        try {
          Navigator.of(context, rootNavigator: true).pop();
        } catch (_) {}
      }

      if (mounted) {
        // عرض رسالة الخطأ
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في معالجة قبول الرحلة: $e'),
            backgroundColor: Colors.red,
          ),
        );

        // عرض حوار بسيط لبدء الرحلة على الرغم من الخطأ
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تم قبول الرحلة!'),
            content: const Text('قبل سائق طلب رحلتك. ستبدأ رحلتك قريبًا.'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _startSimpleRide();
                },
                child: const Text('حسنًا'),
              ),
            ],
          ),
        );
      }
    }
  }

  // Handle shared ride event
  Future<void> _handleRideShared(Map<String, dynamic> data) async {
    final String rideId = data['rideId'] ?? '';
    final String mergedWithRideId = data['mergedWithRideId'] ?? '';
    final String driverId = data['driverId'] ?? '';
    final driverInfo = data['driverInfo'];
    final driverLocation = data['driverLocation'];

    debugPrint(
        'Processing ride sharing: rideId=$rideId, mergedWithRideId=$mergedWithRideId, driverId=$driverId');

    try {
      // عرض مؤشر تحميل معلومات الرحلة
      _showLoadingDialog('جاري تحميل معلومات الرحلة المشتركة...');

      // الحصول على معلومات الرحلة من الخادم
      await _rideService
          .getRideById(mergedWithRideId); // Just to verify the ride exists

      // إغلاق مؤشر التحميل
      if (mounted) {
        try {
          Navigator.of(context, rootNavigator: true).pop();
        } catch (e) {
          debugPrint('Error closing loading dialog: $e');
        }
      }

      // إضافة علامة موقع السائق
      if (driverLocation != null) {
        final driverLatLng = LatLng(
          driverLocation['lat'],
          driverLocation['lng'],
        );

        setState(() {
          _markers.removeWhere((m) => m.markerId.value == 'driver');
          _markers.add(
            Marker(
              markerId: const MarkerId('driver'),
              position: driverLatLng,
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueAzure),
              infoWindow: const InfoWindow(
                title: 'السائق',
                snippet: 'في الطريق إليك (رحلة مشتركة)',
              ),
            ),
          );

          // تحديث حالة الرحلة
          _currentRideId = mergedWithRideId;
          _currentRideStatus = 'shared';
          _currentDriverId = driverId;
          _currentDriverInfo = driverInfo;
          _isSharedRide = true;
        });

        // الاستماع لتحديثات موقع السائق
        _listenForDriverLocationUpdates(driverId);

        // عرض حوار معلومات الرحلة المشتركة
        _showSharedRideInfoDialog();
      }
    } catch (e) {
      debugPrint('خطأ في معالجة مشاركة الرحلة: $e');

      // إغلاق مؤشر التحميل إذا كان مفتوحًا
      if (mounted) {
        try {
          Navigator.of(context, rootNavigator: true).pop();
        } catch (_) {}
      }

      if (mounted) {
        // عرض رسالة الخطأ
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في معالجة مشاركة الرحلة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Show shared ride info dialog
  void _showSharedRideInfoDialog() {
    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('رحلة مشتركة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'سيتم مشاركة رحلتك مع راكب آخر.',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            const Text('• قد يتغير المسار قليلاً لاستيعاب الراكب الآخر'),
            const Text('• قد تستغرق الرحلة وقتاً إضافياً'),
            const Text('• ستحصل على خصم في سعر الرحلة'),
            const SizedBox(height: 16),
            const Text(
              'يمكنك متابعة مسار الرحلة على الخريطة.',
              style: TextStyle(fontStyle: FontStyle.italic),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  // Modern AI-Enhanced Top Bar
  Widget _buildModernTopBar() {
    return Consumer<AIRideProvider>(
      builder: (context, aiProvider, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            borderRadius: BorderRadius.circular(25),
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryColor.withValues(alpha: 0.3),
                blurRadius: 20,
                spreadRadius: 2,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Row(
            children: [
              // Menu Button with Animation
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: IconButton(
                  onPressed: () => _scaffoldKey.currentState?.openDrawer(),
                  icon: const Icon(Icons.menu, color: Colors.white, size: 24),
                ),
              ),

              const SizedBox(width: 12),

              // AI Status with Pulse Animation
              Expanded(
                child: _buildAIStatusChip(aiProvider),
              ),

              const SizedBox(width: 12),

              // Action Buttons
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Nearby Drivers Button
                  _buildActionButton(
                    icon: Icons.local_taxi,
                    onPressed: () => _showNearbyDrivers(),
                    tooltip: 'Nearby Drivers',
                  ),

                  const SizedBox(width: 8),

                  // Language Button
                  _buildActionButton(
                    icon: Icons.language,
                    onPressed: () =>
                        LanguageSelector.showLanguageBottomSheet(context),
                    tooltip: 'Language',
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAIStatusChip(AIRideProvider aiProvider) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: aiProvider.aiServiceConnected
            ? AppTheme.aiActiveColor.withValues(alpha: 0.2)
            : AppTheme.aiInactiveColor.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: aiProvider.aiServiceConnected
              ? AppTheme.aiActiveColor
              : AppTheme.aiInactiveColor,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Pulse Animation for AI Status
          AnimatedContainer(
            duration: const Duration(milliseconds: 1000),
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: aiProvider.aiServiceConnected
                  ? AppTheme.aiActiveColor
                  : AppTheme.aiInactiveColor,
              shape: BoxShape.circle,
            ),
          ),

          const SizedBox(width: 8),

          Flexible(
            child: Text(
              aiProvider.aiServiceConnected ? 'AI Active' : 'Standard Mode',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(12),
        ),
        child: IconButton(
          onPressed: onPressed,
          icon: Icon(icon, color: Colors.white, size: 20),
          constraints: const BoxConstraints(minWidth: 40, minHeight: 40),
        ),
      ),
    );
  }

  void _showNearbyDrivers() {
    if (_currentPosition != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => NearbyDriversScreen(
            userLocation: _currentPosition!,
            searchRadius: 5.0,
          ),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Current location not available'),
        ),
      );
    }
  }

  // Modern Map Style
  String? _getMapStyle() {
    return '''
    [
      {
        "featureType": "all",
        "elementType": "geometry",
        "stylers": [
          {
            "color": "#f5f5f5"
          }
        ]
      },
      {
        "featureType": "all",
        "elementType": "labels.icon",
        "stylers": [
          {
            "visibility": "off"
          }
        ]
      },
      {
        "featureType": "all",
        "elementType": "labels.text.fill",
        "stylers": [
          {
            "color": "#616161"
          }
        ]
      },
      {
        "featureType": "all",
        "elementType": "labels.text.stroke",
        "stylers": [
          {
            "color": "#f5f5f5"
          }
        ]
      },
      {
        "featureType": "poi",
        "elementType": "geometry",
        "stylers": [
          {
            "color": "#eeeeee"
          }
        ]
      },
      {
        "featureType": "road",
        "elementType": "geometry",
        "stylers": [
          {
            "color": "#ffffff"
          }
        ]
      },
      {
        "featureType": "road.highway",
        "elementType": "geometry",
        "stylers": [
          {
            "color": "#dadada"
          }
        ]
      },
      {
        "featureType": "water",
        "elementType": "geometry",
        "stylers": [
          {
            "color": "#4285f4"
          }
        ]
      }
    ]
    ''';
  }

  // Enhanced AI-Powered Driver Selection Modal
  Widget _buildEnhancedDriverSelectionModal(
    List<Map<String, dynamic>> availableDrivers,
    dynamic ride,
    LatLng destination,
  ) {
    return Consumer<AIRideProvider>(
      builder: (context, aiProvider, child) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.85,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFF6366F1),
                Color(0xFF8B5CF6),
              ],
            ),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(30),
              topRight: Radius.circular(30),
            ),
          ),
          child: Column(
            children: [
              // Modern Header with AI Status
              Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    // Handle bar
                    Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Title with AI indicator
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.local_taxi,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'اختر سائقك',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                aiProvider.aiServiceConnected
                                    ? 'مرتب بواسطة الذكاء الاصطناعي'
                                    : 'مرتب حسب المسافة',
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 0.8),
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // AI Status Badge
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: aiProvider.aiServiceConnected
                                ? AppTheme.aiActiveColor
                                : AppTheme.aiInactiveColor,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                aiProvider.aiServiceConnected
                                    ? Icons.psychology
                                    : Icons.location_on,
                                color: Colors.white,
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                aiProvider.aiServiceConnected ? 'AI' : 'STD',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Driver List
              Expanded(
                child: Container(
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30),
                    ),
                  ),
                  child: Column(
                    children: [
                      const SizedBox(height: 20),

                      // AI Insights Banner (if AI is active)
                      if (aiProvider.aiServiceConnected)
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 20),
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            gradient: AppTheme.aiGradient,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Row(
                            children: [
                              const Icon(
                                Icons.auto_awesome,
                                color: Colors.white,
                                size: 20,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      'تحليل ذكي',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                    ),
                                    Text(
                                      'تم ترتيب السائقين حسب أفضل تطابق لرحلتك',
                                      style: TextStyle(
                                        color:
                                            Colors.white.withValues(alpha: 0.9),
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                      const SizedBox(height: 16),

                      // Drivers List
                      Expanded(
                        child: ListView.separated(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          itemCount: availableDrivers.length,
                          separatorBuilder: (context, index) =>
                              const SizedBox(height: 12),
                          itemBuilder: (context, index) {
                            final driver = availableDrivers[index];
                            final isRecommended =
                                index == 0 && aiProvider.aiServiceConnected;

                            return _buildEnhancedDriverCard(
                              driver,
                              isRecommended,
                              () {
                                Navigator.pop(context);
                                _requestSpecificDriver(
                                  ride.id,
                                  driver['id'],
                                  destination,
                                );
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEnhancedDriverCard(
    Map<String, dynamic> driver,
    bool isRecommended,
    VoidCallback onTap,
  ) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color:
                  isRecommended ? AppTheme.aiActiveColor : Colors.grey.shade200,
              width: isRecommended ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: isRecommended
                    ? AppTheme.aiActiveColor.withValues(alpha: 0.1)
                    : Colors.black.withValues(alpha: 0.05),
                blurRadius: isRecommended ? 12 : 8,
                spreadRadius: isRecommended ? 2 : 0,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              // Recommended Badge
              if (isRecommended)
                Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    gradient: AppTheme.aiGradient,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.auto_awesome,
                        color: Colors.white,
                        size: 16,
                      ),
                      const SizedBox(width: 6),
                      const Text(
                        'الأفضل لك',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),

              // Driver Info
              Row(
                children: [
                  // Driver Avatar
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      gradient: AppTheme.primaryGradient,
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: const Icon(
                      Icons.person,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Driver Details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              driver['name'],
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: AppTheme.successColor,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                driver['price'],
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 8),

                        // Rating and Car Info
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.amber.shade100,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Icon(
                                    Icons.star,
                                    color: Colors.amber,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${driver['rating']}',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                '${driver['car']} • ${driver['plate']}',
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                  fontSize: 14,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 8),

                        // Distance and ETA
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.green.shade100,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.location_on,
                                    color: Colors.green.shade700,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    driver['distance'],
                                    style: TextStyle(
                                      color: Colors.green.shade700,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 12),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.orange.shade100,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.access_time,
                                    color: Colors.orange.shade700,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    driver['eta'],
                                    style: TextStyle(
                                      color: Colors.orange.shade700,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء لوحة الإجراءات المحسنة
  Widget _buildEnhancedActionPanel() {
    return Column(
      children: [
        // زر الاقتراحات
        Consumer<SuggestionProvider>(
          builder: (context, provider, _) {
            final suggestionCount = provider.filteredSuggestions.length;
            final hasSuggestions = suggestionCount > 0;

            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: _buildModernActionButton(
                icon: Icons.lightbulb,
                color: hasSuggestions ? Colors.amber : Colors.grey,
                backgroundColor:
                    hasSuggestions ? Colors.amber.withAlpha(51) : Colors.white,
                onPressed: _loadAndShowSuggestions,
                tooltip: 'الاقتراحات',
              ),
            );
          },
        ),

        // زر الإشعارات
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: _buildModernActionButton(
            icon: Icons.notifications_none,
            color: Colors.black,
            backgroundColor: Colors.white,
            onPressed: () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const NotificationPage(),
              ),
            ),
            tooltip: 'الإشعارات',
          ),
        ),

        // زر السائقين القريبين
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: _buildModernActionButton(
            icon: Icons.local_taxi,
            color: Colors.black,
            backgroundColor: Colors.white,
            onPressed: () {
              if (_currentPosition != null) {
                final googleLatLng = _currentPosition!;
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => NearbyDriversScreen(
                      userLocation: googleLatLng,
                      searchRadius: 5.0,
                    ),
                  ),
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Current location not available'),
                  ),
                );
              }
            },
            tooltip: 'السائقين القريبين',
          ),
        ),

        // زر اللغة
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: _buildModernActionButton(
            icon: Icons.language,
            color: Colors.black,
            backgroundColor: Colors.white,
            onPressed: () => LanguageSelector.showLanguageBottomSheet(context),
            tooltip: 'اللغة',
          ),
        ),
      ],
    );
  }

  /// بناء زر إجراء حديث
  Widget _buildModernActionButton({
    required IconData icon,
    required Color color,
    required Color backgroundColor,
    required VoidCallback onPressed,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onPressed,
            borderRadius: BorderRadius.circular(12),
            child: Container(
              width: 48,
              height: 48,
              alignment: Alignment.center,
              child: Icon(
                icon,
                size: 24,
                color: color,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء لوحة الوصول السريع للخدمات
  Widget _buildQuickServicePanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'خدمات سريعة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildServiceCard(
                icon: Icons.directions_car,
                title: 'رحلة',
                subtitle: 'احجز رحلتك',
                color: Colors.blue,
                onTap: () => _showAddressPopup(context),
              ),
              _buildServiceCard(
                icon: Icons.delivery_dining,
                title: 'توصيل',
                subtitle: 'خدمة التوصيل',
                color: Colors.orange,
                onTap: () => _showDeliveryService(),
              ),
              _buildServiceCard(
                icon: Icons.schedule,
                title: 'رحلة مجدولة',
                subtitle: 'احجز مسبقاً',
                color: Colors.green,
                onTap: () => _showScheduledRide(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة خدمة
  Widget _buildServiceCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: color.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    icon,
                    size: 32,
                    color: color,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: color.withValues(alpha: 0.8),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// عرض خدمة التوصيل
  void _showDeliveryService() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.delivery_dining, color: Colors.orange, size: 28),
                const SizedBox(width: 12),
                const Text(
                  'خدمة التوصيل',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            const Text(
              'اختر نوع التوصيل:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                children: [
                  _buildDeliveryOption(
                    icon: Icons.restaurant,
                    title: 'طعام',
                    subtitle: 'توصيل الطعام',
                    color: Colors.red,
                  ),
                  _buildDeliveryOption(
                    icon: Icons.shopping_bag,
                    title: 'تسوق',
                    subtitle: 'توصيل المشتريات',
                    color: Colors.green,
                  ),
                  _buildDeliveryOption(
                    icon: Icons.local_pharmacy,
                    title: 'صيدلية',
                    subtitle: 'توصيل الأدوية',
                    color: Colors.blue,
                  ),
                  _buildDeliveryOption(
                    icon: Icons.card_giftcard,
                    title: 'هدايا',
                    subtitle: 'توصيل الهدايا',
                    color: Colors.purple,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء خيار التوصيل
  Widget _buildDeliveryOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Navigator.pop(context);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('خدمة $title قريباً'),
                backgroundColor: color,
              ),
            );
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  size: 40,
                  color: color,
                ),
                const SizedBox(height: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: color.withValues(alpha: 0.8),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// عرض الرحلة المجدولة
  void _showScheduledRide() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.schedule, color: Colors.green, size: 28),
                const SizedBox(width: 12),
                const Text(
                  'رحلة مجدولة',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            const Text(
              'احجز رحلتك مسبقاً:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.green.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  ListTile(
                    leading: Icon(Icons.calendar_today, color: Colors.green),
                    title: const Text('اختر التاريخ'),
                    subtitle: const Text('حدد تاريخ الرحلة'),
                    trailing: Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      // Show date picker
                    },
                  ),
                  const Divider(),
                  ListTile(
                    leading: Icon(Icons.access_time, color: Colors.green),
                    title: const Text('اختر الوقت'),
                    subtitle: const Text('حدد وقت الرحلة'),
                    trailing: Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      // Show time picker
                    },
                  ),
                  const Divider(),
                  ListTile(
                    leading: Icon(Icons.location_on, color: Colors.green),
                    title: const Text('تحديد المسار'),
                    subtitle: const Text('من والى أين'),
                    trailing: Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      Navigator.pop(context);
                      _showAddressPopup(context);
                    },
                  ),
                ],
              ),
            ),
            const Spacer(),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('ميزة الرحلة المجدولة قريباً'),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'تأكيد الحجز',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// class MapSearchBar extends StatelessWidget {
//   final Function(String) onSearch;
//   final Function(Set<Marker>) onMarkersUpdated;

//   const MapSearchBar({
//     super.key,
//     required this.onSearch,
//     required this.onMarkersUpdated,
//   });

//   @override
//   Widget build(BuildContext context) {
//     final TextEditingController controller = TextEditingController();

//     return Container(
//       padding: const EdgeInsets.symmetric(horizontal: 16),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(8),
//         boxShadow: [
//           BoxShadow(
//             color:
//                 Colors.black.withValues(alpha: 26, red: 0, green: 0, blue: 0),
//             blurRadius: 4,
//             offset: const Offset(0, 2),
//           ),
//         ],
//       ),
//       child: TextField(
//         controller: controller,
//         decoration: const InputDecoration(
//           hintText: 'Search for a location',
//           border: InputBorder.none,
//           prefixIcon: Icon(Icons.search),
//         ),
//         onSubmitted: (value) {
//           if (value.isNotEmpty) {
//             onSearch(value);
//           }
//         },
//       ),
//     );
//   }
// }
