import '../entities/payment_method.dart';

/// Payment Repository Interface
/// Defines the contract for payment data operations
abstract class PaymentRepository {
  /// Get all payment methods for a user
  Future<List<PaymentMethod>> getPaymentMethods(String userId);

  /// Get a specific payment method by ID
  Future<PaymentMethod?> getPaymentMethod(String paymentMethodId);

  /// Add a new payment method
  Future<PaymentMethod> addPaymentMethod(PaymentMethod paymentMethod);

  /// Update an existing payment method
  Future<PaymentMethod> updatePaymentMethod(PaymentMethod paymentMethod);

  /// Delete a payment method
  Future<bool> deletePaymentMethod(String paymentMethodId);

  /// Set a payment method as default
  Future<bool> setDefaultPaymentMethod(String userId, String paymentMethodId);

  /// Get default payment method for a user
  Future<PaymentMethod?> getDefaultPaymentMethod(String userId);

  /// Process a payment transaction
  Future<PaymentTransaction> processPayment({
    required String rideId,
    required String paymentMethodId,
    required double amount,
    required String currency,
    String? description,
    Map<String, dynamic>? metadata,
  });

  /// Get payment transaction by ID
  Future<PaymentTransaction?> getPaymentTransaction(String transactionId);

  /// Get payment transactions for a ride
  Future<List<PaymentTransaction>> getPaymentTransactionsForRide(String rideId);

  /// Get payment transactions for a user
  Future<List<PaymentTransaction>> getPaymentTransactionsForUser(String userId);

  /// Refund a payment transaction
  Future<PaymentTransaction> refundPayment(String transactionId, {double? amount});

  /// Get wallet information
  Future<Wallet?> getWallet(String userId);

  /// Add money to wallet
  Future<Wallet> addMoneyToWallet({
    required String userId,
    required double amount,
    required String paymentMethodId,
  });

  /// Deduct money from wallet
  Future<Wallet> deductMoneyFromWallet({
    required String userId,
    required double amount,
    required String reason,
  });

  /// Get wallet transaction history
  Future<List<WalletTransaction>> getWalletTransactions(String userId);

  /// Validate payment method
  Future<bool> validatePaymentMethod(PaymentMethod paymentMethod);

  /// Check if payment method is supported
  Future<bool> isPaymentMethodSupported(String type);

  /// Get supported payment methods
  Future<List<String>> getSupportedPaymentMethods();

  /// Calculate payment fees
  Future<PaymentFees> calculatePaymentFees({
    required double amount,
    required String paymentMethodType,
    required String currency,
  });

  /// Simulate payment processing (for testing)
  Future<PaymentSimulationResult> simulatePayment({
    required String paymentMethodId,
    required double amount,
    required String currency,
    bool shouldSucceed = true,
  });
}

/// Wallet Transaction Entity
class WalletTransaction {
  final String id;
  final String walletId;
  final String type; // 'credit' or 'debit'
  final double amount;
  final String currency;
  final String description;
  final String? referenceId;
  final DateTime createdAt;
  final Map<String, dynamic>? metadata;

  const WalletTransaction({
    required this.id,
    required this.walletId,
    required this.type,
    required this.amount,
    required this.currency,
    required this.description,
    this.referenceId,
    required this.createdAt,
    this.metadata,
  });

  bool get isCredit => type == 'credit';
  bool get isDebit => type == 'debit';

  String get formattedAmount => '${isCredit ? '+' : '-'}$amount $currency';

  @override
  String toString() {
    return 'WalletTransaction(id: $id, type: $type, amount: $formattedAmount)';
  }
}

/// Payment Fees Entity
class PaymentFees {
  final double processingFee;
  final double serviceFee;
  final double totalFees;
  final String currency;
  final Map<String, dynamic>? breakdown;

  const PaymentFees({
    required this.processingFee,
    required this.serviceFee,
    required this.totalFees,
    required this.currency,
    this.breakdown,
  });

  double get totalAmountWithFees => totalFees;

  String get formattedTotalFees => '$totalFees $currency';

  @override
  String toString() {
    return 'PaymentFees(processing: $processingFee, service: $serviceFee, total: $totalFees $currency)';
  }
}

/// Payment Simulation Result
class PaymentSimulationResult {
  final bool success;
  final String? transactionId;
  final String? errorMessage;
  final PaymentStatus status;
  final DateTime processedAt;
  final Map<String, dynamic>? simulationData;

  const PaymentSimulationResult({
    required this.success,
    this.transactionId,
    this.errorMessage,
    required this.status,
    required this.processedAt,
    this.simulationData,
  });

  @override
  String toString() {
    return 'PaymentSimulationResult(success: $success, status: $status, transactionId: $transactionId)';
  }
}
