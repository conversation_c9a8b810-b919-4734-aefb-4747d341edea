import 'dart:math';
import '../../domain/entities/payment_method.dart';
import '../../domain/repositories/payment_repository.dart';
import '../models/payment_method_model.dart';

/// Payment Repository Implementation with Simulation
/// Provides realistic payment processing simulation for development and testing
class PaymentRepositoryImpl implements PaymentRepository {
  // Simulated data storage
  final Map<String, List<PaymentMethod>> _userPaymentMethods = {};
  final Map<String, PaymentTransaction> _transactions = {};
  final Map<String, Wallet> _wallets = {};
  final Map<String, List<WalletTransaction>> _walletTransactions = {};

  // Simulation settings
  final Random _random = Random();
  final double _simulationFailureRate = 0.1; // 10% failure rate for realism

  @override
  Future<List<PaymentMethod>> getPaymentMethods(String userId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    // Return user's payment methods or create default ones
    if (!_userPaymentMethods.containsKey(userId)) {
      _userPaymentMethods[userId] = _createDefaultPaymentMethods(userId);
    }

    return _userPaymentMethods[userId]!;
  }

  @override
  Future<PaymentMethod?> getPaymentMethod(String paymentMethodId) async {
    await Future.delayed(const Duration(milliseconds: 200));

    for (final methods in _userPaymentMethods.values) {
      try {
        final method = methods.where((m) => m.id == paymentMethodId).first;
        return method;
      } catch (e) {
        // Continue searching
      }
    }
    return null;
  }

  @override
  Future<PaymentMethod> addPaymentMethod(PaymentMethod paymentMethod) async {
    await Future.delayed(const Duration(milliseconds: 800));

    // Simulate validation
    if (!await validatePaymentMethod(paymentMethod)) {
      throw Exception('Invalid payment method');
    }

    final userId =
        paymentMethod.metadata?['user_id'] as String? ?? 'default_user';

    if (!_userPaymentMethods.containsKey(userId)) {
      _userPaymentMethods[userId] = [];
    }

    final newMethod = paymentMethod.copyWith(
      id: 'pm_${DateTime.now().millisecondsSinceEpoch}',
      createdAt: DateTime.now(),
    );

    _userPaymentMethods[userId]!.add(newMethod);
    return newMethod;
  }

  @override
  Future<PaymentMethod> updatePaymentMethod(PaymentMethod paymentMethod) async {
    await Future.delayed(const Duration(milliseconds: 600));

    for (final userId in _userPaymentMethods.keys) {
      final methods = _userPaymentMethods[userId]!;
      final index = methods.indexWhere((m) => m.id == paymentMethod.id);

      if (index != -1) {
        final updatedMethod = paymentMethod.copyWith(updatedAt: DateTime.now());
        methods[index] = updatedMethod;
        return updatedMethod;
      }
    }

    throw Exception('Payment method not found');
  }

  @override
  Future<bool> deletePaymentMethod(String paymentMethodId) async {
    await Future.delayed(const Duration(milliseconds: 400));

    for (final methods in _userPaymentMethods.values) {
      methods.removeWhere((m) => m.id == paymentMethodId);
    }
    return true;
  }

  @override
  Future<bool> setDefaultPaymentMethod(
      String userId, String paymentMethodId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final methods = _userPaymentMethods[userId];
    if (methods == null) return false;

    // Remove default from all methods
    for (int i = 0; i < methods.length; i++) {
      methods[i] = methods[i].copyWith(isDefault: false);
    }

    // Set new default
    final index = methods.indexWhere((m) => m.id == paymentMethodId);
    if (index != -1) {
      methods[index] = methods[index].copyWith(isDefault: true);
      return true;
    }

    return false;
  }

  @override
  Future<PaymentMethod?> getDefaultPaymentMethod(String userId) async {
    await Future.delayed(const Duration(milliseconds: 200));

    final methods = _userPaymentMethods[userId];
    if (methods == null) return null;

    try {
      return methods.where((m) => m.isDefault).first;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<PaymentTransaction> processPayment({
    required String rideId,
    required String paymentMethodId,
    required double amount,
    required String currency,
    String? description,
    Map<String, dynamic>? metadata,
  }) async {
    // Simulate processing time
    await Future.delayed(Duration(milliseconds: 1000 + _random.nextInt(2000)));

    final transactionId = 'txn_${DateTime.now().millisecondsSinceEpoch}';
    final now = DateTime.now();

    // Simulate payment processing with realistic failure scenarios
    final shouldFail = _random.nextDouble() < _simulationFailureRate;
    final status = shouldFail ? PaymentStatus.failed : PaymentStatus.completed;

    final transaction = PaymentTransaction(
      id: transactionId,
      rideId: rideId,
      paymentMethodId: paymentMethodId,
      amount: amount,
      currency: currency,
      status: status,
      description: description,
      createdAt: now,
      processedAt: status == PaymentStatus.completed ? now : null,
      transactionReference: status == PaymentStatus.completed
          ? 'ref_${_random.nextInt(999999).toString().padLeft(6, '0')}'
          : null,
      failureReason: shouldFail ? _getRandomFailureReason() : null,
      metadata: metadata,
    );

    _transactions[transactionId] = transaction;

    // If payment successful and using wallet, deduct balance
    if (status == PaymentStatus.completed) {
      final paymentMethod = await getPaymentMethod(paymentMethodId);
      if (paymentMethod?.type == PaymentMethodType.wallet) {
        await _deductFromWallet(
            paymentMethodId, amount, 'Ride payment: $rideId');
      }
    }

    return transaction;
  }

  @override
  Future<PaymentTransaction?> getPaymentTransaction(
      String transactionId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return _transactions[transactionId];
  }

  @override
  Future<List<PaymentTransaction>> getPaymentTransactionsForRide(
      String rideId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return _transactions.values.where((t) => t.rideId == rideId).toList();
  }

  @override
  Future<List<PaymentTransaction>> getPaymentTransactionsForUser(
      String userId) async {
    await Future.delayed(const Duration(milliseconds: 400));
    // In a real implementation, you'd filter by user ID
    return _transactions.values.toList();
  }

  @override
  Future<PaymentTransaction> refundPayment(String transactionId,
      {double? amount}) async {
    await Future.delayed(const Duration(milliseconds: 1500));

    final originalTransaction = _transactions[transactionId];
    if (originalTransaction == null) {
      throw Exception('Transaction not found');
    }

    final refundAmount = amount ?? originalTransaction.amount;
    final refundId = 'rfnd_${DateTime.now().millisecondsSinceEpoch}';

    final refundTransaction = PaymentTransaction(
      id: refundId,
      rideId: originalTransaction.rideId,
      paymentMethodId: originalTransaction.paymentMethodId,
      amount: -refundAmount, // Negative amount for refund
      currency: originalTransaction.currency,
      status: PaymentStatus.completed,
      description: 'Refund for transaction $transactionId',
      createdAt: DateTime.now(),
      processedAt: DateTime.now(),
      transactionReference:
          'rfnd_${_random.nextInt(999999).toString().padLeft(6, '0')}',
      metadata: {
        'original_transaction_id': transactionId,
        'refund_type': amount != null ? 'partial' : 'full',
      },
    );

    _transactions[refundId] = refundTransaction;
    return refundTransaction;
  }

  @override
  Future<Wallet?> getWallet(String userId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    if (!_wallets.containsKey(userId)) {
      _wallets[userId] = Wallet(
        id: 'wallet_$userId',
        userId: userId,
        balance: 100.0, // Default balance
        currency: 'SAR',
        createdAt: DateTime.now(),
      );
    }

    return _wallets[userId];
  }

  @override
  Future<Wallet> addMoneyToWallet({
    required String userId,
    required double amount,
    required String paymentMethodId,
  }) async {
    await Future.delayed(const Duration(milliseconds: 1200));

    final wallet = await getWallet(userId);
    if (wallet == null) throw Exception('Wallet not found');

    final updatedWallet = wallet.copyWith(
      balance: wallet.balance + amount,
      updatedAt: DateTime.now(),
    );

    _wallets[userId] = updatedWallet;

    // Add wallet transaction record
    await _addWalletTransaction(
      walletId: wallet.id,
      type: 'credit',
      amount: amount,
      description: 'Money added to wallet',
      referenceId: paymentMethodId,
    );

    return updatedWallet;
  }

  @override
  Future<Wallet> deductMoneyFromWallet({
    required String userId,
    required double amount,
    required String reason,
  }) async {
    return await _deductFromWallet(userId, amount, reason);
  }

  @override
  Future<List<WalletTransaction>> getWalletTransactions(String userId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final wallet = await getWallet(userId);
    if (wallet == null) return [];

    return _walletTransactions[wallet.id] ?? [];
  }

  @override
  Future<bool> validatePaymentMethod(PaymentMethod paymentMethod) async {
    await Future.delayed(const Duration(milliseconds: 500));

    // Simulate validation logic
    if (paymentMethod.displayName.isEmpty) return false;
    if (paymentMethod.type.isEmpty) return false;

    // Card-specific validation
    if (paymentMethod.isCard) {
      if (paymentMethod.cardNumber == null ||
          paymentMethod.cardNumber!.length < 16) {
        return false;
      }
      if (paymentMethod.expiryDate == null) return false;
    }

    return true;
  }

  @override
  Future<bool> isPaymentMethodSupported(String type) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return PaymentMethodType.allTypes.contains(type);
  }

  @override
  Future<List<String>> getSupportedPaymentMethods() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return PaymentMethodType.allTypes;
  }

  @override
  Future<PaymentFees> calculatePaymentFees({
    required double amount,
    required String paymentMethodType,
    required String currency,
  }) async {
    await Future.delayed(const Duration(milliseconds: 300));

    double processingFee = 0.0;
    double serviceFee = 0.0;

    // Different fees for different payment methods
    switch (paymentMethodType) {
      case PaymentMethodType.creditCard:
      case PaymentMethodType.debitCard:
        processingFee = amount * 0.025; // 2.5%
        serviceFee = 2.0; // Fixed service fee
        break;
      case PaymentMethodType.paypal:
        processingFee = amount * 0.035; // 3.5%
        serviceFee = 1.5;
        break;
      case PaymentMethodType.wallet:
      case PaymentMethodType.cash:
        processingFee = 0.0;
        serviceFee = 0.0;
        break;
      default:
        processingFee = amount * 0.02; // 2%
        serviceFee = 1.0;
    }

    final totalFees = processingFee + serviceFee;

    return PaymentFees(
      processingFee: processingFee,
      serviceFee: serviceFee,
      totalFees: totalFees,
      currency: currency,
      breakdown: {
        'processing_fee_percentage':
            paymentMethodType == PaymentMethodType.wallet ? 0 : 2.5,
        'service_fee_fixed': serviceFee,
      },
    );
  }

  @override
  Future<PaymentSimulationResult> simulatePayment({
    required String paymentMethodId,
    required double amount,
    required String currency,
    bool shouldSucceed = true,
  }) async {
    // Simulate realistic processing time
    await Future.delayed(Duration(milliseconds: 800 + _random.nextInt(1500)));

    final now = DateTime.now();

    if (shouldSucceed && _random.nextDouble() > _simulationFailureRate) {
      return PaymentSimulationResult(
        success: true,
        transactionId: 'sim_${now.millisecondsSinceEpoch}',
        status: PaymentStatus.completed,
        processedAt: now,
        simulationData: {
          'processing_time_ms': 800 + _random.nextInt(1500),
          'gateway_response': 'approved',
          'authorization_code':
              _random.nextInt(999999).toString().padLeft(6, '0'),
        },
      );
    } else {
      return PaymentSimulationResult(
        success: false,
        errorMessage: _getRandomFailureReason(),
        status: PaymentStatus.failed,
        processedAt: now,
        simulationData: {
          'processing_time_ms': 800 + _random.nextInt(1500),
          'gateway_response': 'declined',
          'error_code': 'E${_random.nextInt(9999).toString().padLeft(4, '0')}',
        },
      );
    }
  }

  // Helper methods
  List<PaymentMethod> _createDefaultPaymentMethods(String userId) {
    final now = DateTime.now();
    return [
      PaymentMethod(
        id: 'pm_visa_$userId',
        type: PaymentMethodType.visa,
        displayName: 'Visa ending in 4242',
        cardNumber: '****************',
        expiryDate: '12/26',
        cardHolderName: 'John Doe',
        isDefault: true,
        createdAt: now,
        metadata: {'user_id': userId},
      ),
      PaymentMethod(
        id: 'pm_wallet_$userId',
        type: PaymentMethodType.wallet,
        displayName: 'My Wallet',
        isDefault: false,
        createdAt: now,
        metadata: {'user_id': userId},
      ),
      PaymentMethod(
        id: 'pm_cash_$userId',
        type: PaymentMethodType.cash,
        displayName: 'Cash',
        isDefault: false,
        createdAt: now,
        metadata: {'user_id': userId},
      ),
    ];
  }

  Future<Wallet> _deductFromWallet(
      String userId, double amount, String reason) async {
    final wallet = await getWallet(userId);
    if (wallet == null) throw Exception('Wallet not found');

    if (!wallet.hasSufficientBalance(amount)) {
      throw Exception('Insufficient wallet balance');
    }

    final updatedWallet = wallet.copyWith(
      balance: wallet.balance - amount,
      updatedAt: DateTime.now(),
    );

    _wallets[userId] = updatedWallet;

    // Add wallet transaction record
    await _addWalletTransaction(
      walletId: wallet.id,
      type: 'debit',
      amount: amount,
      description: reason,
    );

    return updatedWallet;
  }

  Future<void> _addWalletTransaction({
    required String walletId,
    required String type,
    required double amount,
    required String description,
    String? referenceId,
  }) async {
    final transaction = WalletTransaction(
      id: 'wt_${DateTime.now().millisecondsSinceEpoch}',
      walletId: walletId,
      type: type,
      amount: amount,
      currency: 'SAR',
      description: description,
      referenceId: referenceId,
      createdAt: DateTime.now(),
    );

    if (!_walletTransactions.containsKey(walletId)) {
      _walletTransactions[walletId] = [];
    }

    _walletTransactions[walletId]!.add(transaction);
  }

  String _getRandomFailureReason() {
    final reasons = [
      'Insufficient funds',
      'Card expired',
      'Invalid card number',
      'Transaction declined by bank',
      'Network timeout',
      'Card blocked',
      'Invalid CVV',
      'Daily limit exceeded',
    ];
    return reasons[_random.nextInt(reasons.length)];
  }
}
