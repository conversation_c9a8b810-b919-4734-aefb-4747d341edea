import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Enum representing different car types
enum CarType {
  sedan,
  suv,
  hatchback,
  van,
  motorcycle,
  truck,
}

/// A service to handle car assets for the map
class CarAssetService {
  static final CarAssetService _instance = CarAssetService._internal();
  static CarAssetService get instance => _instance;

  CarAssetService._internal();

  // Cache for car icons to avoid reloading
  final Map<CarType, BitmapDescriptor> _carIcons = {};

  /// Get the asset path for a car type
  String _getCarAssetPath(CarType type) {
    switch (type) {
      case CarType.sedan:
        return 'assets/images/cars/sedan_top.png';
      case CarType.suv:
        return 'assets/images/cars/suv_top.png';
      case CarType.hatchback:
        return 'assets/images/cars/hatchback_top.png';
      case CarType.van:
        return 'assets/images/cars/van_top.png';
      case CarType.motorcycle:
        return 'assets/images/cars/motorcycle_top.png';
      case CarType.truck:
        return 'assets/images/cars/truck_top.png';
      default:
        return 'assets/images/cars/sedan_top.png';
    }
  }

  /// Get a car icon for a specific car type
  Future<BitmapDescriptor> getCarIcon(CarType type, {int width = 80}) async {
    // Check if the icon is already cached
    if (_carIcons.containsKey(type)) {
      return _carIcons[type]!;
    }

    try {
      // Try to load the asset
      final Uint8List markerIcon =
          await _getBytesFromAsset(_getCarAssetPath(type), width);
      final BitmapDescriptor icon = BitmapDescriptor.bytes(markerIcon);

      // Cache the icon
      _carIcons[type] = icon;
      return icon;
    } catch (e) {
      debugPrint('Error loading car asset: $e');

      // If loading fails, create a fallback colored car icon
      final BitmapDescriptor fallbackIcon =
          await _createFallbackCarIcon(type, width);
      _carIcons[type] = fallbackIcon;
      return fallbackIcon;
    }
  }

  /// Create a fallback car icon with a color based on the car type
  Future<BitmapDescriptor> _createFallbackCarIcon(
      CarType type, int width) async {
    // Define colors for different car types
    final Color color = _getCarColor(type);

    // Create a canvas to draw the car icon
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);
    final Paint paint = Paint()..color = color;

    // Draw a rounded rectangle for the car body
    final double w = width.toDouble();
    final double h = w * 0.6; // Car height is 60% of width

    final Rect rect = Rect.fromLTWH(0, 0, w, h);
    final RRect roundedRect =
        RRect.fromRectAndRadius(rect, Radius.circular(w * 0.2));
    canvas.drawRRect(roundedRect, paint);

    // Draw windows (darker color)
    final Paint windowPaint = Paint()..color = color.withValues(alpha: 0.7);
    final Rect windowRect = Rect.fromLTWH(w * 0.25, h * 0.2, w * 0.5, h * 0.3);
    final RRect windowRRect =
        RRect.fromRectAndRadius(windowRect, Radius.circular(w * 0.05));
    canvas.drawRRect(windowRRect, windowPaint);

    // Draw wheels
    final Paint wheelPaint = Paint()..color = Colors.black;
    canvas.drawCircle(Offset(w * 0.25, h * 0.8), w * 0.08, wheelPaint);
    canvas.drawCircle(Offset(w * 0.75, h * 0.8), w * 0.08, wheelPaint);

    // Convert to image
    final ui.Picture picture = recorder.endRecording();
    final ui.Image image = await picture.toImage(width, (h).toInt());
    final ByteData? byteData =
        await image.toByteData(format: ui.ImageByteFormat.png);

    if (byteData != null) {
      return BitmapDescriptor.bytes(byteData.buffer.asUint8List());
    } else {
      // If all else fails, use default marker
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);
    }
  }

  /// Get a color for a car type
  Color _getCarColor(CarType type) {
    switch (type) {
      case CarType.sedan:
        return Colors.blue;
      case CarType.suv:
        return Colors.green;
      case CarType.hatchback:
        return Colors.orange;
      case CarType.van:
        return Colors.purple;
      case CarType.motorcycle:
        return Colors.red;
      case CarType.truck:
        return Colors.brown;
      default:
        return Colors.blue;
    }
  }

  /// Get bytes from an asset image
  Future<Uint8List> _getBytesFromAsset(String path, int width) async {
    ByteData data = await rootBundle.load(path);
    ui.Codec codec = await ui.instantiateImageCodec(
      data.buffer.asUint8List(),
      targetWidth: width,
    );
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }

  /// Get a car type from a string
  CarType getCarTypeFromString(String typeString) {
    switch (typeString.toLowerCase()) {
      case 'sedan':
        return CarType.sedan;
      case 'suv':
        return CarType.suv;
      case 'hatchback':
        return CarType.hatchback;
      case 'van':
        return CarType.van;
      case 'motorcycle':
        return CarType.motorcycle;
      case 'truck':
        return CarType.truck;
      default:
        return CarType.sedan;
    }
  }
}
