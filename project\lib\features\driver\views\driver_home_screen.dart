import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:geolocator/geolocator.dart';
import 'package:project/core/services/socket_service.dart';
import 'package:project/core/services/driver_service.dart';
import 'package:project/core/services/ride_service.dart';
import 'package:project/widgets/voice_assistant_button.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/services/driver_voice_service.dart';
import '../../../core/services/automatic_voice_handler.dart';
import '../../../core/services/ride_coordination_service.dart';
import '../../../core/widgets/unified_ride_status_widget.dart';
import '../widgets/voice_enabled_ride_popup.dart';
import '../widgets/ride_timer_dialog.dart';
import '../widgets/ride_request_popup.dart';
import '../../../core/models/ride_request.dart' as models;

class DriverHomeScreen extends StatefulWidget {
  const DriverHomeScreen({super.key});

  @override
  State<DriverHomeScreen> createState() => _DriverHomeScreenState();
}

class _DriverHomeScreenState extends State<DriverHomeScreen> {
  final Completer<GoogleMapController> _mapController = Completer();
  LatLng? _currentPosition;
  final LatLng _defaultPosition = const LatLng(37.7749, -122.4194);
  final Set<Marker> _markers = {};
  List<LatLng> _polylinePoints = [];
  Set<Polyline> _polylines = {};
  bool _isAvailable = true;
  bool _isSocketConnected = false;
  String _connectionStatus = 'Disconnected';
  Timer? _rideRequestTimer;
  Timer? _pendingRidesTimer;
  String? _driverId;
  String? _currentRideId;
  final RideService _rideService = RideService.instance;
  final DriverService _driverService = DriverService.instance;
  final DriverVoiceService _driverVoiceService = DriverVoiceService();
  final AutomaticVoiceHandler _automaticVoiceHandler = AutomaticVoiceHandler();

  // List to store pending ride requests
  final List<RideRequest> _pendingRides = [];

  // Set to track processed ride IDs to avoid duplicates
  final Set<String> _processedRideIds = {};

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();
    _initializeDriver();
    _connectToSocketService();
    _setupSocketListeners();
    _startFetchingPendingRides();
    _initializeVoiceService();
  }

  Future<void> _initializeVoiceService() async {
    try {
      await _driverVoiceService.initialize();
      debugPrint('Driver voice service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing driver voice service: $e');
    }
  }

  // Socket subscriptions
  StreamSubscription<RideRequest>? _newRideRequestSubscription;
  StreamSubscription<Map<String, dynamic>>? _rideErrorSubscription;
  StreamSubscription<Map<String, dynamic>>? _rideSharingAnalysisSubscription;
  StreamSubscription<Map<String, dynamic>>? _rideStopAddedSubscription;
  StreamSubscription<Map<String, dynamic>>? _connectionStatusSubscription;

  Future<void> _initializeDriver() async {
    // Récupérer ou créer un ID de conducteur
    final prefs = await SharedPreferences.getInstance();
    String? driverId = prefs.getString('driver_id');

    if (driverId == null) {
      try {
        // Créer un nouveau conducteur dans la base de données
        driverId = await _driverService.createDriver(
          name: 'Driver ${DateTime.now().millisecondsSinceEpoch}',
          vehicleType: 'Car',
          vehicleNumber: 'ABC-123',
          latitude: _currentPosition?.latitude,
          longitude: _currentPosition?.longitude,
        );

        // Sauvegarder l'ID du conducteur
        await prefs.setString('driver_id', driverId);
      } catch (e) {
        debugPrint('Erreur lors de la création du conducteur: $e');
        // Utiliser un ID temporaire en cas d'échec
        driverId = 'driver_${DateTime.now().millisecondsSinceEpoch}';
      }
    }

    setState(() {
      _driverId = driverId;
    });

    debugPrint('Driver ID: $_driverId');
  }

  void _startFetchingPendingRides() {
    // Récupérer les demandes de trajet en attente toutes les 10 secondes
    _pendingRidesTimer = Timer.periodic(const Duration(seconds: 10), (_) {
      _fetchPendingRides();
    });

    // Récupérer les demandes immédiatement au démarrage
    _fetchPendingRides();
  }

  void _connectToSocketService() {
    // تسجيل السائق كمتاح
    SocketService.instance.connect();

    // Listen to connection status
    _connectionStatusSubscription =
        SocketService.instance.onConnectionStatus.listen((status) {
      if (mounted) {
        setState(() {
          _isSocketConnected = status['connected'] ?? false;
          if (_isSocketConnected) {
            _connectionStatus = 'Connected';
          } else {
            _connectionStatus = status['error'] != null
                ? 'Error: ${status['error']}'
                : 'Disconnected';
          }
        });

        // Show connection status to user
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_connectionStatus),
            backgroundColor: _isSocketConnected ? Colors.green : Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    });

    // الحصول على معرف السائق (يمكن استخدام SharedPreferences أو غيرها)
    _getDriverId().then((driverId) {
      if (driverId.isNotEmpty) {
        SocketService.instance.setDriverAvailable(driverId);

        // الاستماع لطلبات الرحلات الجديدة
        SocketService.instance.onNewRideRequest.listen((rideRequest) {
          if (_isAvailable) {
            _showRideRequestPopup(rideRequest);
          }
        });
      }
    });
  }

  Future<String> _getDriverId() async {
    if (_driverId != null) {
      return _driverId!;
    }

    // Récupérer l'ID du conducteur depuis les préférences
    final prefs = await SharedPreferences.getInstance();
    String? driverId = prefs.getString('driver_id');

    if (driverId == null) {
      // Utiliser un ID temporaire si aucun ID n'est trouvé
      driverId = 'driver_${DateTime.now().millisecondsSinceEpoch}';
    }

    return driverId;
  }

  Future<void> _fetchPendingRides() async {
    if (!_isAvailable || _driverId == null) {
      return;
    }

    try {
      // Récupérer les demandes de trajet en attente
      final pendingRides = await _driverService.getPendingRides();

      if (pendingRides.isNotEmpty) {
        debugPrint(
            'Demandes de trajet en attente trouvées: ${pendingRides.length}');

        // Traiter chaque demande de trajet
        for (final ride in pendingRides) {
          // Vérifier si la demande a déjà été traitée
          final isProcessed = await _isRideAlreadyProcessed(ride['id']);
          if (!isProcessed) {
            // Convertir en objet RideRequest pour l'afficher
            final rideRequest = RideRequest(
              id: ride['id'],
              pickupLocation: ride['pickupLocation'],
              destination: ride['destination'],
              status: ride['status'],
              riderId: ride['riderId'],
            );

            // Afficher la demande de trajet si le conducteur est disponible
            if (_isAvailable && mounted) {
              _showRideRequestPopup(rideRequest);
              break; // Afficher une seule demande à la fois
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Erreur lors de la récupération des demandes de trajet: $e');
    }
  }

  Future<bool> _isRideAlreadyProcessed(String rideId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final processedRides = prefs.getStringList('processed_rides') ?? [];
      return processedRides.contains(rideId);
    } catch (e) {
      debugPrint('Erreur lors de la vérification des demandes traitées: $e');
      return false;
    }
  }

  @override
  void dispose() {
    _rideRequestTimer?.cancel();
    _pendingRidesTimer?.cancel();
    _newRideRequestSubscription?.cancel();
    _rideErrorSubscription?.cancel();
    _rideSharingAnalysisSubscription?.cancel();
    _rideStopAddedSubscription?.cancel();
    _connectionStatusSubscription?.cancel();
    super.dispose();
  }

  // Mark a ride as processed to avoid duplicates
  void _markRideAsProcessed(String rideId) {
    _processedRideIds.add(rideId);
    setState(() {
      _pendingRides.removeWhere((ride) => ride.id == rideId);
    });
  }

  // Decode polyline from encoded string
  List<LatLng> _decodePolyline(String encoded) {
    List<LatLng> points = [];
    int index = 0, len = encoded.length;
    int lat = 0, lng = 0;

    while (index < len) {
      int b, shift = 0, result = 0;
      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlat = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lat += dlat;

      shift = 0;
      result = 0;
      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlng = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lng += dlng;

      points.add(LatLng(lat / 1E5, lng / 1E5));
    }
    return points;
  }

  // Fit map to show all points in the polyline
  void _fitMapToPolyline() async {
    if (_polylinePoints.isEmpty) return;

    final controller = await _mapController.future;

    // Calculate bounds
    double minLat = _polylinePoints.first.latitude;
    double maxLat = _polylinePoints.first.latitude;
    double minLng = _polylinePoints.first.longitude;
    double maxLng = _polylinePoints.first.longitude;

    for (final point in _polylinePoints) {
      if (point.latitude < minLat) minLat = point.latitude;
      if (point.latitude > maxLat) maxLat = point.latitude;
      if (point.longitude < minLng) minLng = point.longitude;
      if (point.longitude > maxLng) maxLng = point.longitude;
    }

    // Add padding
    final padding = 0.01;
    minLat -= padding;
    maxLat += padding;
    minLng -= padding;
    maxLng += padding;

    // Create bounds
    final bounds = LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );

    // Animate camera to show bounds
    controller.animateCamera(
      CameraUpdate.newLatLngBounds(bounds, 50),
    );
  }

  // Show notification for new ride request
  void _showRideRequestNotification(RideRequest rideRequest) {
    // Show a snackbar notification
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('طلب رحلة جديد من ${rideRequest.riderId}'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'عرض',
            onPressed: () {
              _showRideRequestPopup(rideRequest);
            },
          ),
        ),
      );
    }
  }

  // Setup socket listeners for multi-stop rides
  void _setupSocketListeners() {
    // Listen for new ride requests
    _newRideRequestSubscription =
        SocketService.instance.onNewRideRequest.listen((rideRequest) {
      if (mounted && _isAvailable) {
        setState(() {
          // Add to pending rides if not already processed
          if (!_processedRideIds.contains(rideRequest.id)) {
            _pendingRides.add(rideRequest);
          }
        });

        // Show ride request popup immediately
        _showRideRequestPopup(rideRequest);

        // Show notification for new ride request
        _showRideRequestNotification(rideRequest);
      }
    });

    // Listen for ride accepted events
    SocketService.instance.onRideAccepted.listen((data) {
      if (mounted) {
        setState(() {
          // Update UI when ride is accepted
          _handleRideAccepted(data);
        });
      }
    });

    // Listen for ride started events
    SocketService.instance.onRideStarted.listen((rideId) {
      if (mounted) {
        setState(() {
          // Update UI when ride starts
          _handleRideStarted(rideId);
        });
      }
    });

    // Listen for ride errors
    _rideErrorSubscription = SocketService.instance.onRideError.listen((data) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: ${data['message']}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    });

    // Listen for ride sharing analysis
    _rideSharingAnalysisSubscription =
        SocketService.instance.onRideSharingAnalysis.listen((data) {
      if (mounted) {
        _showRideSharingAnalysisDialog(data);
      }
    });

    // Listen for ride stop added
    _rideStopAddedSubscription =
        SocketService.instance.onRideStopAdded.listen((data) {
      if (mounted) {
        // Update the route on the map
        _updateRouteWithNewStop(data);

        // Show notification
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تمت إضافة محطة جديدة إلى الرحلة'),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }

  // Show dialog for ride sharing analysis
  void _showRideSharingAnalysisDialog(Map<String, dynamic> data) {
    final rideId = data['rideId'];
    final analysis = data['analysis'] as Map<String, dynamic>;
    final requireConfirmation = data['requireConfirmation'] ?? false;

    if (!requireConfirmation) return;

    final detourDistance = analysis['detourDistance'] ?? 0;
    final detourDuration = analysis['detourDuration'] ?? 0;

    // Convert to kilometers and minutes
    final detourDistanceKm = (detourDistance / 1000).toStringAsFixed(1);
    final detourDurationMin = (detourDuration / 60).toStringAsFixed(0);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحليل مشاركة الرحلة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ستؤدي إضافة هذه الرحلة إلى تحويل مسارك الحالي:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Text('• مسافة إضافية: $detourDistanceKm كم'),
            Text('• وقت إضافي: $detourDurationMin دقيقة'),
            const SizedBox(height: 16),
            const Text(
              'هل ترغب في قبول هذه الرحلة على الرغم من التحويل؟',
              style: TextStyle(fontStyle: FontStyle.italic),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _getDriverId().then((driverId) {
                SocketService.instance.confirmRideSharing(
                  rideId: rideId,
                  driverId: driverId,
                  confirmed: false,
                );
              });
            },
            child: const Text('رفض'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _getDriverId().then((driverId) {
                SocketService.instance.confirmRideSharing(
                  rideId: rideId,
                  driverId: driverId,
                  confirmed: true,
                );

                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم قبول الرحلة المشتركة!'),
                    backgroundColor: Colors.green,
                  ),
                );
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('قبول'),
          ),
        ],
      ),
    );
  }

  // Update route with new stop
  void _updateRouteWithNewStop(Map<String, dynamic> data) {
    final stops = data['stops'] as List<dynamic>;
    final route = data['route'] as Map<String, dynamic>;

    if (route.containsKey('encodedPolyline')) {
      // Decode polyline
      final encodedPolyline = route['encodedPolyline'] as String;
      final points = _decodePolyline(encodedPolyline);

      setState(() {
        // Update polyline points
        _polylinePoints = points;

        // Update polylines
        _polylines = {
          Polyline(
            polylineId: const PolylineId('route'),
            points: _polylinePoints,
            color: Colors.blue,
            width: 5,
          ),
        };

        // Update markers for all stops
        _markers.clear();

        // Add markers for each stop
        for (final stop in stops) {
          final location = stop['location'] as Map<String, dynamic>;
          final type = stop['type'] as String;
          final riderId = stop['riderId'] as String;

          _markers.add(
            Marker(
              markerId: MarkerId('${type}_${riderId}'),
              position:
                  LatLng(location['lat'] as double, location['lng'] as double),
              icon: BitmapDescriptor.defaultMarkerWithHue(
                type == 'pickup'
                    ? BitmapDescriptor.hueGreen
                    : BitmapDescriptor.hueRed,
              ),
              infoWindow: InfoWindow(
                title: type == 'pickup' ? 'نقطة التقاط' : 'نقطة إنزال',
                snippet: 'الراكب: $riderId',
              ),
            ),
          );
        }
      });

      // Move camera to show the entire route
      if (_polylinePoints.isNotEmpty) {
        _fitMapToPolyline();
      }
    }
  }

  void _showRideRequestPopup(RideRequest rideRequest) {
    if (_currentPosition == null) {
      debugPrint('Current position not available');
      return;
    }

    // Calculate ride details for voice announcement
    final pickupLocation = LatLng(
      rideRequest.pickupLocation['lat'],
      rideRequest.pickupLocation['lng'],
    );

    // Calculate distance to pickup (simplified calculation)
    final distanceToPickup = _calculateDistance(
      _currentPosition!.latitude,
      _currentPosition!.longitude,
      pickupLocation.latitude,
      pickupLocation.longitude,
    );

    // Estimate time (assuming 30 km/h average speed)
    final estimatedTime = (distanceToPickup / 30 * 60).round();

    // Estimate earnings (simplified calculation)
    final estimatedEarnings = distanceToPickup * 2.5;

    // Start automatic voice handling IMMEDIATELY
    _automaticVoiceHandler.handleIncomingRideRequest(
      rideId: rideRequest.id,
      distance: distanceToPickup,
      estimatedTime: estimatedTime,
      estimatedEarnings: estimatedEarnings,
      onAcceptRide: _acceptRide,
      onRejectRide: _rejectRide,
      context: context,
    );

    // Show the visual popup as backup
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => VoiceEnabledRidePopup(
        rideRequest: rideRequest,
        currentPosition: _currentPosition!,
        onAcceptRide: _acceptRide,
        onRejectRide: _rejectRide,
      ),
    );
  }

  // Handle ride accepted event
  void _handleRideAccepted(Map<String, dynamic> data) {
    debugPrint('Driver: Ride accepted - $data');

    // Update current ride state
    setState(() {
      _currentRideId = data['rideId'];
      _isAvailable = false;
    });

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم قبول الرحلة بنجاح!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  // Handle ride started event
  void _handleRideStarted(String rideId) {
    debugPrint('Driver: Ride started - $rideId');

    setState(() {
      _currentRideId = rideId;
      _isAvailable = false;
    });

    // Show ride started message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('بدأت الرحلة!'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  // Accept ride request
  Future<void> _acceptRideRequest(RideRequest rideRequest) async {
    try {
      final driverId = await _getDriverId();

      // Accept the ride via socket
      SocketService.instance.acceptRide(
        rideId: rideRequest.id,
        driverId: driverId,
      );

      // Mark as processed
      _markRideAsProcessed(rideRequest.id);

      // Update UI state
      setState(() {
        _currentRideId = rideRequest.id;
        _isAvailable = false;
      });

      debugPrint('Ride accepted: ${rideRequest.id}');
    } catch (e) {
      debugPrint('Error accepting ride: $e');
    }
  }

  // Reject ride request
  Future<void> _rejectRideRequest(RideRequest rideRequest) async {
    try {
      final driverId = await _getDriverId();

      // Reject the ride via socket
      SocketService.instance.rejectRide(
        rideId: rideRequest.id,
        driverId: driverId,
      );

      // Mark as processed
      _markRideAsProcessed(rideRequest.id);

      debugPrint('Ride rejected: ${rideRequest.id}');
    } catch (e) {
      debugPrint('Error rejecting ride: $e');
    }
  }

  // عنصر معلومات للعرض في نافذة طلب الرحلة
  Widget _infoItem(IconData icon, String value, String label) {
    return Column(
      children: [
        Icon(icon, color: Colors.blue),
        const SizedBox(height: 4),
        Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
      ],
    );
  }

  // حساب المسافة بين نقطتين باستخدام صيغة هافرساين (بالكيلومتر)
  double _calculateDistance(
      double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // نصف قطر الأرض بالكيلومتر

    // تحويل الإحداثيات من درجات إلى راديان
    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);

    // صيغة هافرساين
    final double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) *
            cos(_degreesToRadians(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);
    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  // تحويل من درجات إلى راديان
  double _degreesToRadians(double degrees) {
    return degrees * (pi / 180);
  }

  Future<void> _acceptRide(String rideId) async {
    if (!mounted) return;

    // إغلاق أي نوافذ منبثقة مفتوحة فوراً
    try {
      Navigator.of(context, rootNavigator: true).pop();
    } catch (_) {}

    setState(() {
      _isAvailable = false;
    });

    try {
      // الحصول على معرف السائق
      final driverId = await _getDriverId();

      // تحديث حالة الرحلة عبر API
      final updatedRide = await _rideService.acceptRide(rideId, driverId);

      // إرسال إشعار قبول الرحلة عبر Socket للتحديث في الوقت الفعلي
      SocketService.instance.acceptRide(
        rideId: rideId,
        driverId: driverId,
      );

      // تسجيل الرحلة كمعالجة
      _markRideAsProcessed(rideId);

      if (mounted) {
        // عرض رسالة تأكيد
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم قبول الرحلة! بدء الرحلة...'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }

      // Voice announcement for ride acceptance
      _driverVoiceService.announceRideStatus('accepted');

      // الحصول على إحداثيات نقطة الالتقاط
      final pickupLocation = LatLng(
        updatedRide.pickupLocation['lat'],
        updatedRide.pickupLocation['lng'],
      );

      // الحصول على إحداثيات الوجهة
      final destination = LatLng(
        updatedRide.destination['lat'],
        updatedRide.destination['lng'],
      );

      // Wait a moment before starting the ride to ensure UI is stable
      await Future.delayed(const Duration(milliseconds: 500));

      if (mounted) {
        // بدء الرحلة فوراً مع نظام التنسيق الموحد
        await _startCoordinatedRide(
          rideId: rideId,
          driverId: driverId,
          riderId: updatedRide.riderId, // استخدام معرف الراكب الصحيح
          pickupLocation: pickupLocation,
          destination: destination,
        );
      }
    } catch (e) {
      debugPrint('خطأ أثناء قبول الرحلة: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ أثناء قبول الرحلة: $e'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() {
          _isAvailable = true;
        });
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );

        // إعادة السائق إلى حالة متاح في حالة الخطأ
        setState(() {
          _isAvailable = true;
        });
      }
    }
  }

  // عرض مؤشر التحميل
  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PopScope(
        canPop: false,
        child: AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(message),
            ],
          ),
        ),
      ),
    );
  }

  // بدء الرحلة مع نظام التنسيق الموحد
  Future<void> _startCoordinatedRide({
    required String rideId,
    required String driverId,
    required String riderId,
    required LatLng pickupLocation,
    required LatLng destination,
  }) async {
    try {
      debugPrint('بدء الرحلة المنسقة: $rideId');

      // Initialize coordination service
      await RideCoordinationService.instance.initialize();

      // Start coordinated ride
      await RideCoordinationService.instance.startCoordinatedRide(
        rideId: rideId,
        driverId: driverId,
        riderId: riderId, // استخدام معرف الراكب الصحيح المُمرر من المعاملات
        pickupLocation: pickupLocation,
        destination: destination,
        isDriver: true,
      );

      // رسم المسار على الخريطة
      await _drawRoute(pickupLocation, destination);

      // إضافة علامات على الخريطة
      setState(() {
        _markers.clear();

        // علامة نقطة الالتقاط
        _markers.add(
          Marker(
            markerId: const MarkerId('pickup'),
            position: pickupLocation,
            icon: BitmapDescriptor.defaultMarkerWithHue(
                BitmapDescriptor.hueGreen),
            infoWindow: const InfoWindow(title: 'نقطة الالتقاط'),
          ),
        );

        // علامة الوجهة
        _markers.add(
          Marker(
            markerId: const MarkerId('destination'),
            position: destination,
            icon:
                BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
            infoWindow: const InfoWindow(title: 'الوجهة'),
          ),
        );

        // علامة السائق (الموقع الحالي)
        if (_currentPosition != null) {
          _markers.add(
            Marker(
              markerId: const MarkerId('driver'),
              position: _currentPosition!,
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueBlue),
              infoWindow: const InfoWindow(title: 'موقعي'),
            ),
          );
        }
      });

      // بدء إرسال تحديثات الموقع
      _startLocationUpdates(rideId);

      // إظهار واجهة الرحلة الموحدة
      _showUnifiedRideInterface();

      // Update to driver en route phase
      RideCoordinationService.instance.updateRidePhase(RidePhase.driverEnRoute);

      // محاكاة الوصول إلى نقطة الالتقاط بعد 30 ثانية
      Timer(const Duration(seconds: 30), () {
        if (mounted) {
          _arriveAtPickupCoordinated(
              rideId, driverId, pickupLocation, destination);
        }
      });
    } catch (e) {
      debugPrint('خطأ أثناء بدء الرحلة المنسقة: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ أثناء بدء الرحلة: $e')),
        );
      }
    }
  }

  // إظهار واجهة الرحلة الموحدة
  void _showUnifiedRideInterface() {
    // Add a small delay to ensure the previous dialog is fully closed
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => PopScope(
            canPop: false,
            child: Dialog(
              insetPadding: const EdgeInsets.all(16),
              child: UnifiedRideStatusWidget(
                isDriver: true,
                onCompleteRide: () {
                  if (mounted) {
                    RideCoordinationService.instance.completeRide();
                    Navigator.of(context).pop();
                    setState(() {
                      _isAvailable = true;
                      _markers.clear();
                      _polylines.clear();
                    });
                  }
                },
                onCancelRide: () {
                  if (mounted) {
                    RideCoordinationService.instance
                        .cancelRide('إلغاء من السائق');
                    Navigator.of(context).pop();
                    setState(() {
                      _isAvailable = true;
                      _markers.clear();
                      _polylines.clear();
                    });
                  }
                },
                onContactOther: () {
                  // Implement contact rider functionality
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('ميزة الاتصال بالراكب قريباً')),
                    );
                  }
                },
              ),
            ),
          ),
        );
      }
    });
  }

  // الوصول إلى نقطة الالتقاط مع التنسيق
  void _arriveAtPickupCoordinated(String rideId, String driverId,
      LatLng pickupLocation, LatLng destination) {
    if (mounted) {
      // Update coordination phase
      RideCoordinationService.instance
          .updateRidePhase(RidePhase.arrivedAtPickup);

      // إظهار إشعار الوصول إلى نقطة الالتقاط
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('وصلت إلى نقطة الالتقاط! في انتظار الراكب...'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 3),
        ),
      );

      // Voice announcement
      _driverVoiceService.speak('وصلت إلى نقطة الالتقاط');

      // محاكاة بدء الرحلة إلى الوجهة بعد 15 ثانية
      Timer(const Duration(seconds: 15), () {
        if (mounted) {
          _startToDestinationCoordinated(rideId, driverId, destination);
        }
      });
    }
  }

  // بدء التوجه إلى الوجهة مع التنسيق
  void _startToDestinationCoordinated(
      String rideId, String driverId, LatLng destination) {
    if (mounted) {
      // Update coordination phase
      RideCoordinationService.instance.updateRidePhase(RidePhase.inProgress);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('بدء التوجه إلى الوجهة...'),
          backgroundColor: Colors.blue,
          duration: Duration(seconds: 2),
        ),
      );

      // Voice announcement
      _driverVoiceService.speak('بدء التوجه إلى الوجهة');

      // محاكاة الوصول إلى الوجهة بعد دقيقة واحدة
      Timer(const Duration(minutes: 1), () {
        if (mounted) {
          _arriveAtDestinationCoordinated(rideId, driverId);
        }
      });
    }
  }

  // الوصول إلى الوجهة مع التنسيق
  void _arriveAtDestinationCoordinated(String rideId, String driverId) {
    if (mounted) {
      // Update coordination phase
      RideCoordinationService.instance
          .updateRidePhase(RidePhase.arrivedAtDestination);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('وصلت إلى الوجهة! انتهت الرحلة.'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 3),
        ),
      );

      // Voice announcement
      _driverVoiceService.speak('وصلت إلى الوجهة. انتهت الرحلة');

      // Auto-complete after a short delay
      Timer(const Duration(seconds: 3), () {
        if (mounted) {
          RideCoordinationService.instance.completeRide();
          try {
            Navigator.of(context, rootNavigator: true).pop();
          } catch (_) {}
          setState(() {
            _isAvailable = true;
            _markers.clear();
            _polylines.clear();
          });
        }
      });
    }
  }

  // بدء الرحلة مع مؤقت حقيقي (الطريقة القديمة - محفوظة للتوافق)
  Future<void> _startRideWithRealTimeTimer({
    required String rideId,
    required String driverId,
    required LatLng pickupLocation,
    required LatLng destination,
  }) async {
    try {
      debugPrint('بدء الرحلة مع مؤقت حقيقي: $rideId');

      // رسم المسار على الخريطة
      await _drawRoute(pickupLocation, destination);

      // إضافة علامات على الخريطة
      setState(() {
        _markers.clear();

        // علامة نقطة الالتقاط
        _markers.add(
          Marker(
            markerId: const MarkerId('pickup'),
            position: pickupLocation,
            icon: BitmapDescriptor.defaultMarkerWithHue(
                BitmapDescriptor.hueGreen),
            infoWindow: const InfoWindow(title: 'نقطة الالتقاط'),
          ),
        );

        // علامة الوجهة
        _markers.add(
          Marker(
            markerId: const MarkerId('destination'),
            position: destination,
            icon:
                BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
            infoWindow: const InfoWindow(title: 'الوجهة'),
          ),
        );

        // علامة السائق (الموقع الحالي)
        if (_currentPosition != null) {
          _markers.add(
            Marker(
              markerId: const MarkerId('driver'),
              position: _currentPosition!,
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueBlue),
              infoWindow: const InfoWindow(title: 'موقعي'),
            ),
          );
        }
      });

      // بدء إرسال تحديثات الموقع
      _startLocationUpdates(rideId);

      // إظهار مؤقت الرحلة مع العد التنازلي
      _showRideTimerDialog(rideId, driverId, pickupLocation, destination);

      // محاكاة الوصول إلى نقطة الالتقاط بعد 30 ثانية
      Timer(const Duration(seconds: 30), () {
        if (mounted) {
          _arriveAtPickup(rideId, driverId, pickupLocation, destination);
        }
      });
    } catch (e) {
      debugPrint('خطأ أثناء بدء الرحلة: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ أثناء بدء الرحلة: $e')),
        );
      }
    }
  }

  // إظهار مؤقت الرحلة
  void _showRideTimerDialog(String rideId, String driverId,
      LatLng pickupLocation, LatLng destination) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => RideTimerDialog(
        rideId: rideId,
        driverId: driverId,
        pickupLocation: pickupLocation,
        destination: destination,
        onArriveAtPickup: () =>
            _arriveAtPickup(rideId, driverId, pickupLocation, destination),
        onCompleteRide: () => _completeRide(rideId, driverId),
      ),
    );
  }

  // الوصول إلى نقطة الالتقاط
  void _arriveAtPickup(String rideId, String driverId, LatLng pickupLocation,
      LatLng destination) {
    if (mounted) {
      // إظهار إشعار الوصول إلى نقطة الالتقاط
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('وصلت إلى نقطة الالتقاط! في انتظار الراكب...'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 3),
        ),
      );

      // إرسال إشعار للراكب بوصول السائق
      SocketService.instance.socket?.emit('driver:arrived_at_pickup', {
        'rideId': rideId,
        'driverId': driverId,
        'timestamp': DateTime.now().toIso8601String(),
      });

      // Voice announcement
      _driverVoiceService.speak('وصلت إلى نقطة الالتقاط');

      // محاكاة بدء الرحلة إلى الوجهة بعد 15 ثانية
      Timer(const Duration(seconds: 15), () {
        if (mounted) {
          _startToDestination(rideId, driverId, destination);
        }
      });
    }
  }

  // بدء التوجه إلى الوجهة
  void _startToDestination(String rideId, String driverId, LatLng destination) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('بدء التوجه إلى الوجهة...'),
          backgroundColor: Colors.blue,
          duration: Duration(seconds: 2),
        ),
      );

      // إرسال إشعار بدء الرحلة
      SocketService.instance.startRide(
        rideId: rideId,
        riderConfirmed: true,
      );

      // Voice announcement
      _driverVoiceService.speak('بدء التوجه إلى الوجهة');

      // محاكاة الوصول إلى الوجهة بعد دقيقة واحدة
      Timer(const Duration(minutes: 1), () {
        if (mounted) {
          _arriveAtDestination(rideId, driverId);
        }
      });
    }
  }

  // الوصول إلى الوجهة
  void _arriveAtDestination(String rideId, String driverId) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('وصلت إلى الوجهة! انتهت الرحلة.'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 3),
        ),
      );

      // Voice announcement
      _driverVoiceService.speak('وصلت إلى الوجهة. انتهت الرحلة');

      // إنهاء الرحلة
      _completeRide(rideId, driverId);
    }
  }

  // إنهاء الرحلة
  void _completeRide(String rideId, String driverId) {
    try {
      // إنهاء الرحلة
      SocketService.instance.completeRide(
        rideId: rideId,
        driverId: driverId,
      );

      // إعادة السائق إلى حالة متاح
      setState(() {
        _isAvailable = true;
        _markers.clear();
        _polylines.clear();
      });

      // إغلاق مؤقت الرحلة إذا كان مفتوحاً
      try {
        Navigator.of(context, rootNavigator: true).pop();
      } catch (_) {}

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنهاء الرحلة بنجاح!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }

      debugPrint('تم إنهاء الرحلة: $rideId');
    } catch (e) {
      debugPrint('خطأ أثناء إنهاء الرحلة: $e');
    }
  }

  // بدء الرحلة (الطريقة القديمة - محفوظة للتوافق)
  Future<void> _startRide({
    required String rideId,
    required String driverId,
    required LatLng pickupLocation,
    required LatLng destination,
  }) async {
    if (!mounted) return;

    try {
      // رسم المسار من الموقع الحالي إلى نقطة الالتقاط
      if (_currentPosition != null) {
        await _drawRoute(_currentPosition!, pickupLocation);

        // إعلام الخادم بقبول الرحلة (لكن لا نبدأها تلقائيًا)
        // سيتم بدء الرحلة فقط بعد وصول السائق إلى نقطة الالتقاط وتأكيد الراكب

        // الاستماع لتحديثات موقع السائق (في هذه الحالة، نحن السائق)
        _startLocationUpdates(rideId);

        // الاستماع لتأكيد الراكب
        _listenForRiderConfirmation(rideId);

        // بعد وصول السائق إلى نقطة الالتقاط، إرسال إشعار للراكب
        Future.delayed(const Duration(seconds: 15), () {
          if (mounted) {
            // إظهار إشعار الوصول إلى نقطة الالتقاط
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                    'تم الوصول إلى نقطة الالتقاط. في انتظار تأكيد الراكب...'),
                backgroundColor: Colors.green,
              ),
            );

            // إرسال إشعار للراكب بوصول السائق
            SocketService.instance.socket?.emit('driver:arrived_at_pickup', {
              'rideId': rideId,
              'driverId': driverId,
              'timestamp': DateTime.now().toIso8601String(),
            });

            // عرض زر لبدء الرحلة يدويًا
            _showStartRideConfirmation(rideId, pickupLocation, destination);
          }
        });

        // عند الوصول إلى الوجهة، إنهاء الرحلة
        Future.delayed(const Duration(seconds: 30), () {
          if (mounted) {
            // إظهار إشعار الوصول إلى الوجهة
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم الوصول إلى الوجهة. انتهت الرحلة.'),
                backgroundColor: Colors.green,
              ),
            );

            // إنهاء الرحلة
            SocketService.instance.completeRide(
              rideId: rideId,
              driverId: driverId,
            );

            // إعادة السائق إلى حالة متاح
            setState(() {
              _isAvailable = true;
            });
          }
        });
      }
    } catch (e) {
      debugPrint('خطأ أثناء بدء الرحلة: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ أثناء بدء الرحلة: $e')),
        );
      }
    }
  }

  // بدء إرسال تحديثات الموقع
  void _startLocationUpdates(String rideId) {
    // في تطبيق حقيقي، سنستخدم موقع GPS الفعلي
    // هنا نقوم بمحاكاة تحديثات الموقع

    // تحديث الموقع كل ثانيتين
    Timer.periodic(const Duration(seconds: 2), (timer) {
      // التحقق من أن الواجهة لا تزال موجودة
      if (!mounted) {
        timer.cancel();
        return;
      }

      // التحقق من وجود نقاط المسار
      if (_polylinePoints.isEmpty) {
        timer.cancel();
        return;
      }

      // اختيار نقطة عشوائية من المسار لمحاكاة حركة السائق
      final randomIndex = Random().nextInt(_polylinePoints.length);
      final randomPoint = _polylinePoints[randomIndex];

      // إرسال تحديث الموقع
      SocketService.instance.updateDriverLocation(
        rideId: rideId,
        location: {
          'lat': randomPoint.latitude,
          'lng': randomPoint.longitude,
        },
      );

      // تحديث موقع السائق على الخريطة
      setState(() {
        // إزالة علامة السائق السابقة
        _markers.removeWhere((m) => m.markerId.value == 'driver');

        // إضافة علامة السائق الجديدة
        _markers.add(
          Marker(
            markerId: const MarkerId('driver'),
            position: randomPoint,
            icon: BitmapDescriptor.defaultMarkerWithHue(
                BitmapDescriptor.hueAzure),
          ),
        );
      });

      // إلغاء المؤقت بعد 30 ثانية (محاكاة انتهاء الرحلة)
      if (timer.tick > 15) {
        timer.cancel();
      }
    });
  }

  // This method is now replaced by the one defined earlier
  void _persistProcessedRide(String rideId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final processedRides = prefs.getStringList('processed_rides') ?? [];
      processedRides.add(rideId);
      await prefs.setStringList('processed_rides', processedRides);
    } catch (e) {
      debugPrint('Erreur lors du marquage de la demande comme traitée: $e');
    }
  }

  Future<void> _rejectRide(String rideId) async {
    try {
      // عرض مؤشر التحميل
      _showLoadingDialog('جاري رفض الرحلة...');

      final driverId = await _getDriverId();

      // تحديث حالة الرحلة عبر API
      await _rideService.updateRide(rideId, {
        'status': 'rejected',
        'rejectedBy': driverId,
      });

      // إرسال إشعار رفض الرحلة عبر Socket للتحديث في الوقت الفعلي
      SocketService.instance.rejectRide(
        rideId: rideId,
        driverId: driverId,
      );

      // تسجيل الرحلة كمعالجة
      _markRideAsProcessed(rideId);

      // إغلاق مؤشر التحميل
      if (mounted) {
        Navigator.of(context, rootNavigator: true).pop();
      }

      if (mounted) {
        // عرض رسالة تأكيد
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم رفض الرحلة'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 2),
          ),
        );
      }

      // Voice announcement for ride rejection
      _driverVoiceService.announceRideStatus('rejected');

      debugPrint('Driver $driverId rejected ride $rideId');
    } catch (e) {
      debugPrint('خطأ أثناء رفض الرحلة: $e');

      // إغلاق مؤشر التحميل إذا كان مفتوحًا
      if (mounted) {
        try {
          Navigator.of(context, rootNavigator: true).pop();
        } catch (_) {}
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _drawRoute(LatLng start, LatLng end) async {
    try {
      final String url = 'https://router.project-osrm.org/route/v1/driving/'
          '${start.longitude},${start.latitude};${end.longitude},${end.latitude}'
          '?overview=full&geometries=polyline';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['code'] != 'Ok') {
          throw Exception('OSRM API error: ${data["code"]}');
        }

        if (data['routes'] != null && data['routes'].isNotEmpty) {
          final route = data['routes'][0];
          final encodedPolyline = route['geometry'];
          final points = _decodePolyline(encodedPolyline);

          setState(() {
            _polylinePoints = points;
            _markers.clear();
            _polylines.clear();

            // Add start marker
            _markers.add(
              Marker(
                markerId: const MarkerId('start'),
                position: start,
                icon: BitmapDescriptor.defaultMarkerWithHue(
                    BitmapDescriptor.hueBlue),
              ),
            );

            // Add end marker
            _markers.add(
              Marker(
                markerId: const MarkerId('end'),
                position: end,
                icon: BitmapDescriptor.defaultMarkerWithHue(
                    BitmapDescriptor.hueRed),
              ),
            );

            // Add polyline
            _polylines.add(
              Polyline(
                polylineId: const PolylineId('route'),
                points: points,
                color: Colors.blue,
                width: 4,
              ),
            );
          });

          // Move camera to show the route
          if (_mapController.isCompleted) {
            final controller = await _mapController.future;
            controller.animateCamera(
              CameraUpdate.newLatLngBounds(
                LatLngBounds(
                  southwest: LatLng(
                    min(start.latitude, end.latitude) - 0.05,
                    min(start.longitude, end.longitude) - 0.05,
                  ),
                  northeast: LatLng(
                    max(start.latitude, end.latitude) + 0.05,
                    max(start.longitude, end.longitude) + 0.05,
                  ),
                ),
                100,
              ),
            );
          }
        }
      }
    } catch (e) {
      debugPrint('Error drawing route: $e');
    }
  }

  // This method is now replaced by the one defined earlier

  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('Location services disabled');
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.deniedForever) {
        debugPrint('Location permissions permanently denied');
        return;
      }

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          debugPrint('Location permissions denied');
          return;
        }
      }

      Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(seconds: 10),
        ),
      );

      setState(() {
        _currentPosition = LatLng(position.latitude, position.longitude);
        _markers.add(
          Marker(
            markerId: const MarkerId('current_location'),
            position: _currentPosition!,
            icon:
                BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
          ),
        );
      });

      if (_mapController.isCompleted) {
        final controller = await _mapController.future;
        controller
            .animateCamera(CameraUpdate.newLatLngZoom(_currentPosition!, 14.0));
      }
    } catch (e) {
      debugPrint('Error getting location: $e');
    }
  }

  // الاستماع لتأكيد الراكب
  void _listenForRiderConfirmation(String rideId) {
    SocketService.instance.onRiderConfirmation.listen((data) {
      if (data['rideId'] == rideId && data['confirmed'] == true) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تأكيد الراكب! جاري بدء الرحلة...'),
              backgroundColor: Colors.green,
            ),
          );

          // بدء الرحلة بعد تأكيد الراكب
          SocketService.instance.startRide(
            rideId: rideId,
            riderConfirmed: true,
          );
        }
      }
    });
  }

  // عرض تأكيد بدء الرحلة
  void _showStartRideConfirmation(
      String rideId, LatLng pickupLocation, LatLng destination) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.directions_car, color: Colors.green),
            SizedBox(width: 8),
            Text('بدء الرحلة'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('هل وصلت إلى نقطة الالتقاط وتريد بدء الرحلة؟'),
            SizedBox(height: 8),
            Text('تأكد من وجود الراكب قبل بدء الرحلة.',
                style: TextStyle(color: Colors.grey, fontSize: 12)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('انتظار'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);

              // بدء الرحلة يدويًا
              SocketService.instance.startRide(
                rideId: rideId,
                riderConfirmed: false, // لم يؤكد الراكب بعد
              );

              // رسم المسار إلى الوجهة
              _drawRoute(pickupLocation, destination);

              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم بدء الرحلة!'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('بدء الرحلة'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Driver Mode'),
        actions: [
          Switch(
            value: _isAvailable,
            onChanged: (value) {
              setState(() {
                _isAvailable = value;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(_isAvailable
                      ? 'You are now online'
                      : 'You are now offline'),
                ),
              );
            },
          ),
          VoiceAssistantButton(
            size: 40,
            showLabel: false,
            onCommand: (command) {
              // معالجة الأوامر الصوتية للسائق
              if (command.contains('قبول الرحلة') ||
                  command.contains('موافق')) {
                // تنفيذ قبول الرحلة
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                      content: Text('تم قبول الرحلة بواسطة الأمر الصوتي')),
                );
              } else if (command.contains('رفض') || command.contains('إلغاء')) {
                // تنفيذ رفض الرحلة
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                      content: Text('تم رفض الرحلة بواسطة الأمر الصوتي')),
                );
              }
            },
          ),
          // Voice Command Test Button
          IconButton(
            onPressed: () {
              _automaticVoiceHandler.testVoiceCommands();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('اختبار الأوامر الصوتية - قل "قبول" أو "رفض"'),
                  duration: Duration(seconds: 3),
                ),
              );
            },
            icon: const Icon(Icons.mic_external_on),
            tooltip: 'اختبار الأوامر الصوتية',
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Stack(
        children: [
          GoogleMap(
            initialCameraPosition: CameraPosition(
              target: _currentPosition ?? _defaultPosition,
              zoom: 14.0,
            ),
            myLocationEnabled: true,
            myLocationButtonEnabled: false,
            markers: _markers,
            polylines: _polylines,
            onMapCreated: (GoogleMapController controller) {
              _mapController.complete(controller);
            },
          ),
          Positioned(
            bottom: 16,
            left: 16,
            right: 16,
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      _isAvailable ? 'You are online' : 'You are offline',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: _isAvailable ? Colors.green : Colors.red,
                      ),
                    ),
                    const SizedBox(height: 8),
                    // Connection status indicator
                    Row(
                      children: [
                        Icon(
                          _isSocketConnected ? Icons.wifi : Icons.wifi_off,
                          color: _isSocketConnected ? Colors.green : Colors.red,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _connectionStatus,
                          style: TextStyle(
                            fontSize: 12,
                            color:
                                _isSocketConnected ? Colors.green : Colors.red,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _isAvailable
                          ? 'Waiting for ride requests...'
                          : 'Go online to receive ride requests',
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
