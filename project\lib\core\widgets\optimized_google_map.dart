import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../services/map_performance_service.dart';

/// خريطة Google محسنة للأداء
/// تستخدم تحسينات الأداء المتقدمة لتحسين تجربة المستخدم
class OptimizedGoogleMap extends StatefulWidget {
  final CameraPosition initialCameraPosition;
  final Set<Marker> markers;
  final Set<Polyline> polylines;
  final Set<Polygon> polygons;
  final Set<Circle> circles;
  final void Function(GoogleMapController)? onMapCreated;
  final void Function(LatLng)? onTap;
  final void Function(LatLng)? onLongPress;
  final void Function(CameraPosition)? onCameraMove;
  final void Function()? onCameraIdle;
  final MapType mapType;
  final bool myLocationEnabled;
  final bool myLocationButtonEnabled;
  final bool zoomControlsEnabled;
  final bool zoomGesturesEnabled;
  final bool scrollGesturesEnabled;
  final bool tiltGesturesEnabled;
  final bool rotateGesturesEnabled;
  final bool mapToolbarEnabled;
  final bool compassEnabled;
  final bool trafficEnabled;
  final bool buildingsEnabled;
  final String? mapStyle;
  final EdgeInsets padding;
  final double minMaxZoomPreference;
  final double maxMaxZoomPreference;

  const OptimizedGoogleMap({
    Key? key,
    required this.initialCameraPosition,
    this.markers = const <Marker>{},
    this.polylines = const <Polyline>{},
    this.polygons = const <Polygon>{},
    this.circles = const <Circle>{},
    this.onMapCreated,
    this.onTap,
    this.onLongPress,
    this.onCameraMove,
    this.onCameraIdle,
    this.mapType = MapType.normal,
    this.myLocationEnabled = false,
    this.myLocationButtonEnabled = false,
    this.zoomControlsEnabled = false,
    this.zoomGesturesEnabled = true,
    this.scrollGesturesEnabled = true,
    this.tiltGesturesEnabled = false,
    this.rotateGesturesEnabled = true,
    this.mapToolbarEnabled = false,
    this.compassEnabled = true,
    this.trafficEnabled = false,
    this.buildingsEnabled = true,
    this.mapStyle,
    this.padding = EdgeInsets.zero,
    this.minMaxZoomPreference = 0.0,
    this.maxMaxZoomPreference = 20.0,
  }) : super(key: key);

  @override
  State<OptimizedGoogleMap> createState() => _OptimizedGoogleMapState();
}

class _OptimizedGoogleMapState extends State<OptimizedGoogleMap>
    with WidgetsBindingObserver {
  final MapPerformanceService _performanceService = MapPerformanceService.instance;
  GoogleMapController? _controller;
  
  // تحسينات الأداء
  Set<Marker> _optimizedMarkers = {};
  Set<Polyline> _optimizedPolylines = {};
  Timer? _updateTimer;
  LatLng? _lastCameraPosition;
  DateTime? _lastUpdate;
  
  // إعدادات الأداء المحسنة
  late Map<String, dynamic> _optimizedSettings;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _performanceService.initialize();
    _optimizedSettings = _performanceService.getOptimizedMapSettings();
    _startOptimizedUpdates();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _updateTimer?.cancel();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    switch (state) {
      case AppLifecycleState.paused:
        // تفعيل وضع توفير الطاقة عند إيقاف التطبيق مؤقتاً
        _performanceService.setPerformanceMode(lowPower: true);
        _updateOptimizedSettings();
        break;
      case AppLifecycleState.resumed:
        // العودة إلى الوضع العادي عند استئناف التطبيق
        _performanceService.setPerformanceMode(lowPower: false);
        _updateOptimizedSettings();
        break;
      default:
        break;
    }
  }

  /// بدء التحديثات المحسنة
  void _startOptimizedUpdates() {
    _updateTimer = Timer.periodic(
      _performanceService.getOptimalUpdateInterval(),
      (_) => _updateOptimizedElements(),
    );
  }

  /// تحديث العناصر المحسنة
  void _updateOptimizedElements() {
    final now = DateTime.now();
    
    // تحديث فقط إذا مر وقت كافٍ منذ آخر تحديث
    if (_lastUpdate != null &&
        now.difference(_lastUpdate!).inMilliseconds < 500) {
      return;
    }

    setState(() {
      // تحسين العلامات
      _optimizedMarkers = _performanceService.optimizeMarkers(
        widget.markers,
        _lastCameraPosition,
      );

      // تحسين المسارات
      _optimizedPolylines = _performanceService.optimizePolylines(widget.polylines);
    });

    _lastUpdate = now;
  }

  /// تحديث الإعدادات المحسنة
  void _updateOptimizedSettings() {
    setState(() {
      _optimizedSettings = _performanceService.getOptimizedMapSettings();
    });
  }

  /// معالج إنشاء الخريطة
  void _onMapCreated(GoogleMapController controller) {
    _controller = controller;
    widget.onMapCreated?.call(controller);
  }

  /// معالج حركة الكاميرا
  void _onCameraMove(CameraPosition position) {
    _lastCameraPosition = position.target;
    widget.onCameraMove?.call(position);
  }

  /// معالج توقف الكاميرا
  void _onCameraIdle() {
    // تحديث العناصر عند توقف الكاميرا
    _updateOptimizedElements();
    widget.onCameraIdle?.call();
  }

  @override
  Widget build(BuildContext context) {
    return GoogleMap(
      initialCameraPosition: widget.initialCameraPosition,
      markers: _optimizedMarkers,
      polylines: _optimizedPolylines,
      polygons: widget.polygons,
      circles: widget.circles,
      onMapCreated: _onMapCreated,
      onTap: widget.onTap,
      onLongPress: widget.onLongPress,
      onCameraMove: _onCameraMove,
      onCameraIdle: _onCameraIdle,
      mapType: widget.mapType,
      
      // استخدام الإعدادات المحسنة
      myLocationEnabled: _optimizedSettings['myLocationEnabled'] ?? widget.myLocationEnabled,
      myLocationButtonEnabled: _optimizedSettings['myLocationButtonEnabled'] ?? widget.myLocationButtonEnabled,
      zoomControlsEnabled: _optimizedSettings['zoomControlsEnabled'] ?? widget.zoomControlsEnabled,
      zoomGesturesEnabled: widget.zoomGesturesEnabled,
      scrollGesturesEnabled: widget.scrollGesturesEnabled,
      tiltGesturesEnabled: _optimizedSettings['tiltGesturesEnabled'] ?? widget.tiltGesturesEnabled,
      rotateGesturesEnabled: _optimizedSettings['rotateGesturesEnabled'] ?? widget.rotateGesturesEnabled,
      mapToolbarEnabled: _optimizedSettings['mapToolbarEnabled'] ?? widget.mapToolbarEnabled,
      compassEnabled: _optimizedSettings['compassEnabled'] ?? widget.compassEnabled,
      trafficEnabled: _optimizedSettings['trafficEnabled'] ?? widget.trafficEnabled,
      buildingsEnabled: _optimizedSettings['buildingsEnabled'] ?? widget.buildingsEnabled,
      
      style: widget.mapStyle,
      padding: widget.padding,
      minMaxZoomPreference: MinMaxZoomPreference(
        widget.minMaxZoomPreference,
        widget.maxMaxZoomPreference,
      ),
    );
  }
}

/// مؤشر أداء الخريطة
class MapPerformanceIndicator extends StatefulWidget {
  const MapPerformanceIndicator({Key? key}) : super(key: key);

  @override
  State<MapPerformanceIndicator> createState() => _MapPerformanceIndicatorState();
}

class _MapPerformanceIndicatorState extends State<MapPerformanceIndicator> {
  final MapPerformanceService _performanceService = MapPerformanceService.instance;
  Timer? _updateTimer;
  Map<String, dynamic> _stats = {};

  @override
  void initState() {
    super.initState();
    _updateTimer = Timer.periodic(
      const Duration(seconds: 2),
      (_) => _updateStats(),
    );
  }

  @override
  void dispose() {
    _updateTimer?.cancel();
    super.dispose();
  }

  void _updateStats() {
    setState(() {
      _stats = _performanceService.getPerformanceStats();
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_stats.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(8),
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'أداء الخريطة',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),
          if (_stats['isHighPerformanceMode'] == true)
            const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.speed, color: Colors.green, size: 12),
                SizedBox(width: 4),
                Text('أداء عالي', style: TextStyle(color: Colors.green, fontSize: 10)),
              ],
            ),
          if (_stats['isLowPowerMode'] == true)
            const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.battery_saver, color: Colors.orange, size: 12),
                SizedBox(width: 4),
                Text('توفير طاقة', style: TextStyle(color: Colors.orange, fontSize: 10)),
              ],
            ),
          Text(
            'تحديثات: ${_stats['updateCount'] ?? 0}',
            style: const TextStyle(color: Colors.white70, fontSize: 10),
          ),
          Text(
            'فترة: ${_stats['optimalUpdateInterval'] ?? 0}ث',
            style: const TextStyle(color: Colors.white70, fontSize: 10),
          ),
        ],
      ),
    );
  }
}
