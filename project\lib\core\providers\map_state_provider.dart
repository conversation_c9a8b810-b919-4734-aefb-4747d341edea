import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:project/features/map/helpers/map_drivers_helper.dart';

/// مزود حالة الخريطة
/// يدير حالة الخريطة والعلامات والمسارات
class MapStateProvider extends ChangeNotifier {
  // حالة الخريطة
  bool _isMapInitialized = false;
  bool get isMapInitialized => _isMapInitialized;

  // متحكم الخريطة
  final Completer<GoogleMapController> _mapController =
      Completer<GoogleMapController>();
  Completer<GoogleMapController> get mapController => _mapController;

  // الموقع الحالي
  LatLng? _currentPosition;
  LatLng? get currentPosition => _currentPosition;

  // الموقع الافتراضي (سيتم استخدامه إذا لم يتم العثور على الموقع الحالي)
  final LatLng _defaultPosition = const LatLng(24.7136, 46.6753); // الرياض
  LatLng get defaultPosition => _defaultPosition;

  // مجموعة العلامات على الخريطة
  final Set<Marker> _markers = {};
  Set<Marker> get markers => _markers;

  // مجموعة المسارات على الخريطة
  final Set<Polyline> _polylines = {};
  Set<Polyline> get polylines => _polylines;

  // نقاط المسار
  List<LatLng> _routePoints = [];
  List<LatLng> get routePoints => _routePoints;

  // حالة التحميل
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  // رسالة الخطأ
  String? _error;
  String? get error => _error;

  /// تهيئة الخريطة
  void initializeMap(GoogleMapController controller) {
    if (!_mapController.isCompleted) {
      _mapController.complete(controller);
      _isMapInitialized = true;
      notifyListeners();
    }
  }

  /// الحصول على الموقع الحالي
  Future<void> getCurrentLocation() async {
    _setLoading(true);
    try {
      // التحقق من أذونات الموقع
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('تم رفض أذونات الموقع');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('تم رفض أذونات الموقع بشكل دائم');
      }

      // الحصول على الموقع الحالي
      final position = await Geolocator.getCurrentPosition(
          locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10,
      ));

      _currentPosition = LatLng(position.latitude, position.longitude);

      // إضافة علامة للموقع الحالي
      _markers.clear();
      _markers.add(
        Marker(
          markerId: const MarkerId('current_location'),
          position: _currentPosition!,
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
          infoWindow: const InfoWindow(title: 'موقعك الحالي'),
        ),
      );

      // تحريك الكاميرا إلى الموقع الحالي
      if (_isMapInitialized) {
        final controller = await _mapController.future;
        controller
            .animateCamera(CameraUpdate.newLatLngZoom(_currentPosition!, 15));
      }

      notifyListeners();
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل السائقين القريبين
  Future<void> loadNearbyDrivers({double radius = 5.0}) async {
    if (_currentPosition == null) {
      await getCurrentLocation();
    }

    if (_currentPosition != null) {
      _setLoading(true);
      try {
        final driverMarkers = await MapDriversHelper.loadNearbyDriversAsMarkers(
          location: _currentPosition!,
          radius: radius,
          onError: (error) {
            _error = error;
            notifyListeners();
          },
        );

        _markers.addAll(driverMarkers);
        notifyListeners();
      } catch (e) {
        _error = e.toString();
        notifyListeners();
      } finally {
        _setLoading(false);
      }
    }
  }

  /// البحث عن موقع بالعنوان
  Future<void> searchLocation(String address) async {
    _setLoading(true);
    try {
      List<Location> locations = await locationFromAddress(address);
      if (locations.isNotEmpty) {
        Location location = locations.first;
        LatLng searchedPosition = LatLng(location.latitude, location.longitude);

        if (_isMapInitialized) {
          final controller = await _mapController.future;
          controller
              .animateCamera(CameraUpdate.newLatLngZoom(searchedPosition, 15));
        }

        _markers.add(
          Marker(
            markerId: const MarkerId('searched_location'),
            position: searchedPosition,
            icon:
                BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
            infoWindow: InfoWindow(title: address),
          ),
        );

        notifyListeners();
      }
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    } finally {
      _setLoading(false);
    }
  }

  /// إضافة علامة
  void addMarker(Marker marker) {
    _markers.add(marker);
    notifyListeners();
  }

  /// إزالة علامة
  void removeMarker(MarkerId markerId) {
    _markers.removeWhere((marker) => marker.markerId == markerId);
    notifyListeners();
  }

  /// مسح جميع العلامات
  void clearMarkers() {
    _markers.clear();
    notifyListeners();
  }

  /// إضافة مسار
  void addPolyline(Polyline polyline) {
    _polylines.add(polyline);
    notifyListeners();
  }

  /// مسح جميع المسارات
  void clearPolylines() {
    _polylines.clear();
    notifyListeners();
  }

  /// تعيين نقاط المسار
  void setRoutePoints(List<LatLng> points) {
    _routePoints = points;
    notifyListeners();
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// تحريك الخريطة إلى موقع محدد
  Future<void> animateToLocation(LatLng location, {double zoom = 15.0}) async {
    if (_isMapInitialized) {
      try {
        final controller = await _mapController.future;
        controller.animateCamera(CameraUpdate.newLatLngZoom(location, zoom));
      } catch (e) {
        _error = e.toString();
        notifyListeners();
      }
    }
  }
}
