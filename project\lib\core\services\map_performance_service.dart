import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// خدمة تحسين أداء الخريطة
/// تدير تحسينات الأداء للخرائط والرسوم المتحركة
class MapPerformanceService {
  static final MapPerformanceService _instance = MapPerformanceService._internal();
  static MapPerformanceService get instance => _instance;

  MapPerformanceService._internal();

  // إعدادات الأداء
  static const Duration _defaultUpdateInterval = Duration(seconds: 5);
  static const Duration _fastUpdateInterval = Duration(seconds: 2);
  static const Duration _slowUpdateInterval = Duration(seconds: 10);
  static const int _maxMarkersPerUpdate = 20;
  static const double _significantMovementThreshold = 10.0; // meters

  // حالة الأداء
  bool _isHighPerformanceMode = false;
  bool _isLowPowerMode = false;
  DateTime? _lastUpdate;
  int _updateCount = 0;

  // مؤقتات التحديث
  Timer? _locationUpdateTimer;
  Timer? _markersUpdateTimer;
  Timer? _performanceMonitorTimer;

  /// تهيئة خدمة الأداء
  void initialize({
    bool highPerformanceMode = false,
    bool lowPowerMode = false,
  }) {
    _isHighPerformanceMode = highPerformanceMode;
    _isLowPowerMode = lowPowerMode;
    _startPerformanceMonitoring();
  }

  /// بدء مراقبة الأداء
  void _startPerformanceMonitoring() {
    _performanceMonitorTimer = Timer.periodic(
      const Duration(minutes: 1),
      (_) => _analyzePerformance(),
    );
  }

  /// تحليل الأداء وتعديل الإعدادات
  void _analyzePerformance() {
    final now = DateTime.now();
    
    // تحليل معدل التحديثات
    if (_lastUpdate != null) {
      final timeSinceLastUpdate = now.difference(_lastUpdate!);
      
      // إذا كانت التحديثات بطيئة، قم بتقليل التردد
      if (timeSinceLastUpdate.inSeconds > 30) {
        _enableLowPowerMode();
      } else if (timeSinceLastUpdate.inSeconds < 5 && !_isHighPerformanceMode) {
        _enableHighPerformanceMode();
      }
    }

    _lastUpdate = now;
    _updateCount++;
  }

  /// تفعيل وضع الأداء العالي
  void _enableHighPerformanceMode() {
    _isHighPerformanceMode = true;
    _isLowPowerMode = false;
    debugPrint('🚀 High Performance Mode Enabled');
  }

  /// تفعيل وضع توفير الطاقة
  void _enableLowPowerMode() {
    _isLowPowerMode = true;
    _isHighPerformanceMode = false;
    debugPrint('🔋 Low Power Mode Enabled');
  }

  /// الحصول على فترة التحديث المثلى
  Duration getOptimalUpdateInterval() {
    if (_isHighPerformanceMode) {
      return _fastUpdateInterval;
    } else if (_isLowPowerMode) {
      return _slowUpdateInterval;
    } else {
      return _defaultUpdateInterval;
    }
  }

  /// تحسين مجموعة العلامات
  Set<Marker> optimizeMarkers(Set<Marker> markers, LatLng? userLocation) {
    if (markers.length <= _maxMarkersPerUpdate) {
      return markers;
    }

    // إذا كان لدينا موقع المستخدم، قم بترتيب العلامات حسب المسافة
    if (userLocation != null) {
      final markersList = markers.toList();
      markersList.sort((a, b) {
        final distanceA = _calculateDistance(userLocation, a.position);
        final distanceB = _calculateDistance(userLocation, b.position);
        return distanceA.compareTo(distanceB);
      });

      // إرجاع أقرب العلامات فقط
      return markersList.take(_maxMarkersPerUpdate).toSet();
    }

    // إذا لم يكن لدينا موقع المستخدم، قم بإرجاع عينة عشوائية
    final markersList = markers.toList();
    markersList.shuffle();
    return markersList.take(_maxMarkersPerUpdate).toSet();
  }

  /// تحسين المسارات
  Set<Polyline> optimizePolylines(Set<Polyline> polylines) {
    final optimizedPolylines = <Polyline>{};

    for (final polyline in polylines) {
      final optimizedPoints = _simplifyPolylinePoints(polyline.points);
      
      optimizedPolylines.add(
        polyline.copyWith(
          pointsParam: optimizedPoints,
        ),
      );
    }

    return optimizedPolylines;
  }

  /// تبسيط نقاط المسار لتحسين الأداء
  List<LatLng> _simplifyPolylinePoints(List<LatLng> points) {
    if (points.length <= 50) {
      return points; // لا حاجة للتبسيط
    }

    final simplified = <LatLng>[];
    const double tolerance = 0.0001; // حوالي 10 متر

    simplified.add(points.first);

    for (int i = 1; i < points.length - 1; i++) {
      final distance = _calculateDistance(simplified.last, points[i]);
      
      if (distance > tolerance) {
        simplified.add(points[i]);
      }
    }

    simplified.add(points.last);
    return simplified;
  }

  /// حساب المسافة بين نقطتين
  double _calculateDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371000; // نصف قطر الأرض بالمتر
    final double lat1Rad = point1.latitude * math.pi / 180;
    final double lat2Rad = point2.latitude * math.pi / 180;
    final double deltaLatRad = (point2.latitude - point1.latitude) * math.pi / 180;
    final double deltaLngRad = (point2.longitude - point1.longitude) * math.pi / 180;

    final double a = math.sin(deltaLatRad / 2) * math.sin(deltaLatRad / 2) +
        math.cos(lat1Rad) * math.cos(lat2Rad) *
        math.sin(deltaLngRad / 2) * math.sin(deltaLngRad / 2);
    final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));

    return earthRadius * c;
  }

  /// تحديد ما إذا كان التحديث ضرورياً
  bool shouldUpdate(LatLng? lastPosition, LatLng? newPosition) {
    if (lastPosition == null || newPosition == null) {
      return true;
    }

    final distance = _calculateDistance(lastPosition, newPosition);
    return distance > _significantMovementThreshold;
  }

  /// تحسين إعدادات الكاميرا
  CameraUpdate optimizeCameraUpdate(LatLng target, double zoom) {
    // تقليل مستوى التكبير في وضع توفير الطاقة
    if (_isLowPowerMode && zoom > 16) {
      zoom = 16;
    }

    return CameraUpdate.newLatLngZoom(target, zoom);
  }

  /// تحسين إعدادات الخريطة
  Map<String, dynamic> getOptimizedMapSettings() {
    return {
      'myLocationEnabled': !_isLowPowerMode,
      'myLocationButtonEnabled': false,
      'zoomControlsEnabled': false,
      'mapToolbarEnabled': false,
      'compassEnabled': !_isLowPowerMode,
      'rotateGesturesEnabled': !_isLowPowerMode,
      'tiltGesturesEnabled': false, // تعطيل الإمالة لتحسين الأداء
      'buildingsEnabled': !_isLowPowerMode,
      'trafficEnabled': false, // تعطيل حركة المرور لتحسين الأداء
    };
  }

  /// الحصول على إحصائيات الأداء
  Map<String, dynamic> getPerformanceStats() {
    return {
      'isHighPerformanceMode': _isHighPerformanceMode,
      'isLowPowerMode': _isLowPowerMode,
      'updateCount': _updateCount,
      'lastUpdate': _lastUpdate?.toIso8601String(),
      'optimalUpdateInterval': getOptimalUpdateInterval().inSeconds,
    };
  }

  /// تنظيف الموارد
  void dispose() {
    _locationUpdateTimer?.cancel();
    _markersUpdateTimer?.cancel();
    _performanceMonitorTimer?.cancel();
  }

  /// إعادة تعيين إعدادات الأداء
  void reset() {
    _isHighPerformanceMode = false;
    _isLowPowerMode = false;
    _updateCount = 0;
    _lastUpdate = null;
  }

  /// تفعيل وضع الأداء يدوياً
  void setPerformanceMode({
    bool? highPerformance,
    bool? lowPower,
  }) {
    if (highPerformance != null) {
      _isHighPerformanceMode = highPerformance;
      if (highPerformance) _isLowPowerMode = false;
    }
    
    if (lowPower != null) {
      _isLowPowerMode = lowPower;
      if (lowPower) _isHighPerformanceMode = false;
    }
  }
}

/// امتداد لتحسين Polyline
extension PolylineOptimization on Polyline {
  Polyline copyWith({
    List<LatLng>? pointsParam,
    String? polylineIdParam,
    bool? consumeTapEventsParam,
    Color? colorParam,
    bool? geodesicParam,
    JointType? jointTypeParam,
    List<PatternItem>? patternsParam,
    Cap? startCapParam,
    Cap? endCapParam,
    int? widthParam,
    int? zIndexParam,
    VoidCallback? onTapParam,
  }) {
    return Polyline(
      polylineId: polylineIdParam ?? polylineId,
      points: pointsParam ?? points,
      consumeTapEvents: consumeTapEventsParam ?? consumeTapEvents,
      color: colorParam ?? color,
      geodesic: geodesicParam ?? geodesic,
      jointType: jointTypeParam ?? jointType,
      patterns: patternsParam ?? patterns,
      startCap: startCapParam ?? startCap,
      endCap: endCapParam ?? endCap,
      width: widthParam ?? width,
      zIndex: zIndexParam ?? zIndex,
      onTap: onTapParam ?? onTap,
    );
  }
}
