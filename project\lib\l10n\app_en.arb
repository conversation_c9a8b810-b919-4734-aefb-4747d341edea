{"@@locale": "en", "appTitle": "Smart Ride Sharing", "@appTitle": {"description": "The title of the application"}, "welcome": "Welcome", "@welcome": {"description": "Welcome message"}, "getStarted": "Get Started", "@getStarted": {"description": "Get started button text"}, "login": "<PERSON><PERSON>", "@login": {"description": "Login button text"}, "signUp": "Sign Up", "@signUp": {"description": "Sign up button text"}, "email": "Email", "@email": {"description": "Email field label"}, "password": "Password", "@password": {"description": "Password field label"}, "confirmPassword": "Confirm Password", "@confirmPassword": {"description": "Confirm password field label"}, "firstName": "First Name", "@firstName": {"description": "First name field label"}, "lastName": "Last Name", "@lastName": {"description": "Last name field label"}, "phoneNumber": "Phone Number", "@phoneNumber": {"description": "Phone number field label"}, "forgotPassword": "Forgot Password?", "@forgotPassword": {"description": "Forgot password link text"}, "home": "Home", "@home": {"description": "Home tab label"}, "profile": "Profile", "@profile": {"description": "Profile tab label"}, "history": "History", "@history": {"description": "History tab label"}, "wallet": "Wallet", "@wallet": {"description": "Wallet tab label"}, "offers": "Offers", "@offers": {"description": "Offers tab label"}, "notifications": "Notifications", "@notifications": {"description": "Notifications tab label"}, "whereWouldYouGo": "Where would you go?", "@whereWouldYouGo": {"description": "Destination input placeholder"}, "searchForPlaces": "Search for places...", "@searchForPlaces": {"description": "Search input placeholder"}, "selectAddress": "Select address", "@selectAddress": {"description": "Address selection title"}, "from": "From", "@from": {"description": "From location label"}, "to": "To", "@to": {"description": "To location label"}, "previewRoute": "Preview Route", "@previewRoute": {"description": "Preview route button text"}, "requestRide": "Request Ride", "@requestRide": {"description": "Request ride button text"}, "recentPlaces": "Recent places", "@recentPlaces": {"description": "Recent places section title"}, "work": "Work", "@work": {"description": "Work location label"}, "chooseDriver": "<PERSON><PERSON>", "@chooseDriver": {"description": "Choose driver title"}, "availableDriversNearby": "Available drivers nearby", "@availableDriversNearby": {"description": "Available drivers subtitle"}, "rating": "Rating", "@rating": {"description": "Driver rating label"}, "distance": "Distance", "@distance": {"description": "Distance label"}, "estimatedTime": "Estimated Time", "@estimatedTime": {"description": "Estimated time label"}, "price": "Price", "@price": {"description": "Price label"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "confirm": "Confirm", "@confirm": {"description": "Confirm button text"}, "accept": "Accept", "@accept": {"description": "Accept button text"}, "reject": "Reject", "@reject": {"description": "Reject button text"}, "waiting": "Waiting", "@waiting": {"description": "Waiting status text"}, "searchingForDriver": "Searching for driver...", "@searchingForDriver": {"description": "Searching for driver message"}, "pleaseWaitWhileWeSearch": "Please wait while we find the best driver for you", "@pleaseWaitWhileWeSearch": {"description": "Searching for driver description"}, "rideAccepted": "Ride accepted! Preparing your ride...", "@rideAccepted": {"description": "Ride accepted message"}, "rideRejected": "Ride request rejected", "@rideRejected": {"description": "<PERSON> rejected message"}, "rideStarted": "Ride started! Driver is on the way to destination...", "@rideStarted": {"description": "Ride started message"}, "driverUnavailable": "Driver is currently unavailable", "@driverUnavailable": {"description": "Driver unavailable message"}, "noDriversAvailable": "No drivers available at the moment", "@noDriversAvailable": {"description": "No drivers available message"}, "driverArrivedAtPickup": "Driver has arrived at pickup location", "@driverArrivedAtPickup": {"description": "Driver arrived at pickup message"}, "driverArrivedAtDestination": "Driver has arrived at destination", "@driverArrivedAtDestination": {"description": "Driver arrived at destination message"}, "error": "Error", "@error": {"description": "Error label"}, "ok": "OK", "@ok": {"description": "OK button text"}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "loading": "Loading...", "@loading": {"description": "Loading message"}, "noNotifications": "No notifications available", "@noNotifications": {"description": "No notifications message"}, "refreshAndStartOver": "Refresh and Start Over", "@refreshAndStartOver": {"description": "Refresh button text"}, "selectRole": "Select Your Role", "@selectRole": {"description": "Role selection title"}, "rider": "Rider", "@rider": {"description": "Rider role"}, "driver": "Driver", "@driver": {"description": "Driver role"}, "car": "Car", "@car": {"description": "Car service type"}, "shared ride": "Shared Ride", "@sharedRide": {"description": "Shared ride service type"}, "delivery": "Delivery", "@delivery": {"description": "Delivery service type"}}